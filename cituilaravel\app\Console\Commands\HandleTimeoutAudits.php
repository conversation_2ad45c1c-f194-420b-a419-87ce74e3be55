<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\Citui\AuditService;

class HandleTimeoutAudits extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'citui:handle-timeout-audits';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '处理超时的审核记录';

    protected AuditService $auditService;

    /**
     * Create a new command instance.
     */
    public function __construct(AuditService $auditService)
    {
        parent::__construct();
        $this->auditService = $auditService;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('开始处理超时审核...');
        
        try {
            $count = $this->auditService->handleTimeoutAudits();
            
            if ($count > 0) {
                $this->info("成功处理了 {$count} 个超时审核记录");
            } else {
                $this->info('没有发现超时的审核记录');
            }
            
            return Command::SUCCESS;
            
        } catch (\Exception $e) {
            $this->error('处理超时审核时发生错误: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }
}