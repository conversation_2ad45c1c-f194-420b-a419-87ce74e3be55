# 用户管理相关表结构设计说明文档

## 概述

本文档详细说明了次推应用用户管理模块的数据库表结构设计，包括用户基础信息表、用户登录记录表、用户关系表和管理员用户表。设计遵循数据库规范化原则，确保数据完整性、一致性和高性能。

## 表结构设计详情

### 1. 用户基础信息表 (ct_users)

#### 业务用途
- 存储用户的基本信息和账户状态
- 支持手机号和微信登录
- 管理用户实名认证状态
- 记录用户积分和推荐关系
- 支持用户状态管理和软删除

#### 关键字段设计考虑

**身份标识字段：**
- `id`: 使用bigint unsigned确保足够的用户容量
- `phone`: 作为主要登录凭证，设置唯一索引
- `invite_code`: 用户邀请码，支持推广功能

**认证相关字段：**
- `password`: MD5加密存储，支持密码登录
- `wechat_openid/unionid`: 支持微信登录集成
- `real_name/id_card`: 支持实名认证功能
- `is_verified`: 标记实名认证状态

**积分系统字段：**
- `total_points`: 用户累计获得的总积分
- `available_points`: 当前可用积分（扣除已使用部分）

**推荐关系字段：**
- `referrer_id`: 推荐人用户ID，支持多级推荐
- `referrer_level`: 推荐层级，便于奖励计算

**状态管理字段：**
- `status`: 用户账户状态（正常/禁用/待审核）
- `is_deleted`: 软删除标记
- `deleted_at`: 删除时间，支持数据恢复

#### 索引设计说明
- **主键索引**: `id` - 自动创建，保证唯一性
- **唯一索引**: `phone`, `invite_code` - 防止重复注册
- **外键索引**: `referrer_id` - 优化推荐关系查询
- **状态索引**: `status`, `is_verified` - 优化状态筛选
- **时间索引**: `created_at` - 优化注册时间排序
- **微信索引**: `wechat_openid` - 优化微信登录查询

### 2. 用户登录记录表 (ct_user_logins)

#### 业务用途
- 记录用户每次登录的详细信息
- 支持安全审计和异常登录检测
- 收集用户设备和网络环境数据
- 统计用户活跃度和使用习惯
- 支持会话管理和在线状态跟踪

#### 关键字段设计考虑

**登录方式字段：**
- `login_type`: 区分密码登录、验证码登录、微信登录
- `login_status`: 记录登录成功或失败状态
- `failure_reason`: 登录失败时记录具体原因

**设备信息字段：**
- `device_type/model`: 设备类型和型号，用于兼容性分析
- `device_id`: 设备唯一标识，用于设备管理
- `os_version`: 操作系统版本，用于功能适配
- `app_version`: APP版本号，用于版本分析

**网络环境字段：**
- `login_ip`: 登录IP地址，用于地理位置分析
- `login_location`: 地理位置信息
- `network_type`: 网络类型（WiFi/4G/5G）
- `user_agent`: 浏览器或客户端信息

**会话管理字段：**
- `session_id`: 会话标识，用于会话跟踪
- `logout_time`: 退出时间
- `session_duration`: 会话持续时间

#### 索引设计说明
- **主键索引**: `id` - 自动创建
- **外键索引**: `user_id` - 优化用户登录记录查询
- **IP索引**: `login_ip` - 支持IP地址分析
- **设备索引**: `device_id` - 支持设备管理
- **状态索引**: `login_status` - 优化成功/失败统计
- **时间索引**: `created_at` - 优化时间范围查询

### 3. 用户关系表 (ct_user_relations)

#### 业务用途
- 管理用户之间的推荐关系
- 支持多级推荐体系
- 记录推荐奖励和积分发放
- 跟踪邀请码使用情况
- 支持推荐关系的激活和禁用

#### 关键字段设计考虑

**关系定义字段：**
- `user_id`: 被推荐用户ID
- `referrer_id`: 推荐人用户ID
- `relation_type`: 关系类型（直接/间接推荐）
- `relation_level`: 推荐层级（1级/2级/3级等）

**绑定信息字段：**
- `bind_time`: 关系建立时间
- `bind_ip/device`: 绑定时的IP和设备信息
- `invite_code_used`: 使用的邀请码

**奖励管理字段：**
- `reward_points`: 推荐奖励积分
- `is_active`: 关系是否有效
- `status`: 关系状态管理

#### 索引设计说明
- **主键索引**: `id` - 自动创建
- **唯一索引**: `user_id + referrer_id` - 防止重复关系
- **外键索引**: `user_id`, `referrer_id` - 优化关系查询
- **层级索引**: `relation_level` - 优化层级统计
- **邀请码索引**: `invite_code_used` - 优化邀请码查询
- **时间索引**: `bind_time` - 优化时间排序

### 4. 管理员用户表 (ct_admin_users)

#### 业务用途
- 管理系统管理员账户
- 支持角色和权限管理
- 记录管理员操作日志
- 支持管理员层级管理
- 提供安全的后台访问控制

#### 关键字段设计考虑

**身份认证字段：**
- `username`: 管理员登录用户名，唯一标识
- `password`: 加密存储的登录密码
- `real_name`: 管理员真实姓名

**角色权限字段：**
- `role_type`: 角色类型（超级管理员/普通管理员/审核员）
- `permissions`: JSON格式存储的权限列表
- `is_super_admin`: 超级管理员标记

**组织信息字段：**
- `department`: 所属部门
- `position`: 职位信息
- `created_by`: 创建人ID，支持管理员层级

**安全管理字段：**
- `status`: 账户状态（正常/禁用/锁定）
- `password_updated_at`: 密码更新时间
- `last_login_time/ip`: 最后登录信息

#### 索引设计说明
- **主键索引**: `id` - 自动创建
- **唯一索引**: `username` - 保证用户名唯一
- **联系方式索引**: `email`, `phone` - 支持联系方式查询
- **角色索引**: `role_type` - 优化角色筛选
- **状态索引**: `status` - 优化状态筛选
- **创建人索引**: `created_by` - 支持管理员层级查询
- **时间索引**: `created_at` - 优化创建时间排序

## 表关联关系设计

### 1. 用户推荐关系
```sql
ct_users.referrer_id → ct_users.id (自关联)
ct_user_relations.user_id → ct_users.id
ct_user_relations.referrer_id → ct_users.id
```

### 2. 用户登录关系
```sql
ct_user_logins.user_id → ct_users.id
```

### 3. 管理员层级关系
```sql
ct_admin_users.created_by → ct_admin_users.id (自关联)
```

## 数据完整性约束

### 1. 外键约束
- 用户推荐关系：确保推荐人存在
- 登录记录：确保用户存在
- 管理员创建关系：确保创建人存在

### 2. 唯一性约束
- 用户手机号唯一
- 用户邀请码唯一
- 管理员用户名唯一
- 用户关系唯一（防止重复绑定）

### 3. 检查约束
- 状态字段值范围检查
- 性别字段值范围检查
- 角色类型值范围检查

## 性能优化考虑

### 1. 索引优化
- 为常用查询字段创建索引
- 使用复合索引优化多条件查询
- 避免过多索引影响写入性能

### 2. 分区策略
- 登录记录表可按时间分区
- 用户关系表可按用户ID哈希分区

### 3. 查询优化
- 使用覆盖索引减少回表查询
- 合理使用LIMIT限制结果集大小
- 避免全表扫描和复杂子查询

## 扩展性设计

### 1. 预留字段
- 各表预留扩展字段用于未来功能
- JSON字段存储灵活的配置信息

### 2. 软删除机制
- 重要数据使用软删除保证数据安全
- 支持数据恢复和审计需求

### 3. 版本兼容
- 字段设计考虑向后兼容
- 支持平滑的数据库升级

## 安全性考虑

### 1. 数据加密
- 密码字段MD5加密存储
- 敏感信息可考虑AES加密

### 2. 访问控制
- 通过角色和权限控制数据访问
- 记录关键操作的审计日志

### 3. 数据脱敏
- 生产环境敏感数据脱敏
- 测试环境使用虚拟数据

## 维护建议

### 1. 定期维护
- 定期清理过期的登录记录
- 优化表结构和索引
- 监控表空间使用情况

### 2. 备份策略
- 定期全量备份重要数据
- 增量备份日常变更数据
- 测试备份恢复流程

### 3. 监控告警
- 监控数据库性能指标
- 设置异常登录告警
- 监控用户增长趋势