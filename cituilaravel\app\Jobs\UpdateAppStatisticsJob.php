<?php
declare(strict_types=1);

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Services\Citui\AppStatisticsService;
use Illuminate\Support\Facades\Log;

class UpdateAppStatisticsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected int $appId;
    protected string $statisticsType;

    /**
     * Create a new job instance.
     *
     * @param int $appId
     * @param string $statisticsType rating|view|download
     */
    public function __construct(int $appId, string $statisticsType = 'rating')
    {
        $this->appId = $appId;
        $this->statisticsType = $statisticsType;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(AppStatisticsService $statisticsService)
    {
        try {
            Log::info("开始更新APP统计", [
                'app_id' => $this->appId,
                'type' => $this->statisticsType
            ]);

            switch ($this->statisticsType) {
                case 'rating':
                    $result = $statisticsService->updateRatingStatistics($this->appId);
                    break;
                default:
                    Log::warning("未知的统计类型: {$this->statisticsType}");
                    return;
            }

            if ($result['success']) {
                Log::info("APP统计更新成功", [
                    'app_id' => $this->appId,
                    'type' => $this->statisticsType,
                    'data' => $result['data']
                ]);
            } else {
                Log::error("APP统计更新失败", [
                    'app_id' => $this->appId,
                    'type' => $this->statisticsType,
                    'message' => $result['message']
                ]);
            }
        } catch (\Exception $e) {
            Log::error("APP统计更新异常", [
                'app_id' => $this->appId,
                'type' => $this->statisticsType,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // 重新抛出异常，让队列系统处理重试
            throw $e;
        }
    }

    /**
     * Handle a job failure.
     *
     * @param  \Throwable  $exception
     * @return void
     */
    public function failed(\Throwable $exception)
    {
        Log::error("APP统计更新任务最终失败", [
            'app_id' => $this->appId,
            'type' => $this->statisticsType,
            'error' => $exception->getMessage()
        ]);
    }
}