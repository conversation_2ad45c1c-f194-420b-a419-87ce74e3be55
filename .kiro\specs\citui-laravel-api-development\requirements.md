# CitUI Laravel后端API开发需求文档

## 项目概述

基于对CitUI项目的全面分析，需要为次推应用开发完整的Laravel 9后端API系统。该系统将支持APP评测、线索分享、用户管理、积分系统等核心功能，为前端uni-app应用提供数据支持。

## 需求列表

### 需求1：项目架构分析与设计

**用户故事：** 作为开发人员，我需要深入分析现有项目结构和前端需求，以便设计符合项目规范的API架构。

#### 验收标准

1. WHEN 开始开发时 THEN 系统应完整分析前端页面功能需求
2. WHEN 分析API需求文档时 THEN 系统应识别与现有Laravel代码规范的冲突点
3. WHEN 设计API架构时 THEN 系统应遵循现有Laravel项目的代码风格和响应格式
4. WHEN 创建模块结构时 THEN 系统应建立独立的citui模块避免污染现有代码

### 需求2：数据库架构验证与优化

**用户故事：** 作为开发人员，我需要验证现有数据库设计是否满足前端功能需求，并进行必要的调整。

#### 验收标准

1. WHEN 验证数据库表结构时 THEN 系统应检查23个核心表是否满足前端功能需求
2. WHEN 发现设计缺陷时 THEN 系统应提出具体的表结构调整建议
3. WHEN 优化表设计时 THEN 系统应保持无外键约束、无存储过程的设计风格
4. WHEN 设计表关系时 THEN 系统应确保支持多态关联和业务解耦

### 需求3：用户认证与授权系统

**用户故事：** 作为用户，我需要能够注册、登录和管理个人信息，以便使用应用的各项功能。

#### 验收标准

1. WHEN 用户注册时 THEN 系统应支持手机号验证码注册并发放注册奖励积分
2. WHEN 用户登录时 THEN 系统应记录登录日志并生成API Token
3. WHEN 用户修改信息时 THEN 系统应支持头像上传、密码修改等功能
4. WHEN 用户管理关系时 THEN 系统应支持关注、好友等社交功能
5. WHEN API调用时 THEN 系统应使用Laravel Sanctum进行Token认证

### 需求4：APP信息管理系统

**用户故事：** 作为用户，我需要浏览和搜索APP信息，以便选择合适的APP进行评测。

#### 验收标准

1. WHEN 浏览APP时 THEN 系统应提供分类筛选、搜索、排序功能
2. WHEN 查看APP详情时 THEN 系统应显示完整信息包括截图、评分、下载量等
3. WHEN 获取推荐APP时 THEN 系统应根据推荐算法返回精选APP列表
4. WHEN 统计数据时 THEN 系统应记录APP查看次数和用户行为

### 需求5：评测报告系统

**用户故事：** 作为用户，我需要提交和查看APP评测报告，以便分享使用体验并获得积分奖励。

#### 验收标准

1. WHEN 提交评测报告时 THEN 系统应支持完整的报告信息包括截图、评分、测试数据
2. WHEN 审核评测报告时 THEN 系统应提供自动审核和人工审核流程
3. WHEN 查看评测列表时 THEN 系统应支持多维度筛选和排序
4. WHEN 互动评测时 THEN 系统应支持点赞、评论等社交功能
5. WHEN 奖励发放时 THEN 系统应根据审核结果发放相应积分

### 需求6：线索分享系统

**用户故事：** 作为用户，我需要发布和查看放水线索，以便分享收益机会并获得反馈。

#### 验收标准

1. WHEN 发布线索时 THEN 系统应支持详细的线索信息包括步骤、截图、风险等级
2. WHEN 查看线索时 THEN 系统应显示成功率、收益统计等关键指标
3. WHEN 提交反馈时 THEN 系统应记录用户的尝试结果和实际收益
4. WHEN 统计分析时 THEN 系统应实时更新线索的成功率和收益数据
5. WHEN 线索过期时 THEN 系统应自动处理过期线索的状态变更

### 需求7：积分奖励系统

**用户故事：** 作为用户，我需要通过各种行为获得积分并进行兑换，以便获得实际收益。

#### 验收标准

1. WHEN 执行奖励行为时 THEN 系统应根据积分规则自动发放相应积分
2. WHEN 查看积分记录时 THEN 系统应提供详细的积分变化历史
3. WHEN 兑换奖励时 THEN 系统应支持多种奖励类型的兑换功能
4. WHEN 管理积分时 THEN 系统应支持积分过期、调整等管理功能

### 需求8：文件管理系统

**用户故事：** 作为用户，我需要上传各种文件（头像、截图等），以便完善个人信息和内容展示。

#### 验收标准

1. WHEN 上传文件时 THEN 系统应支持多种文件类型和批量上传
2. WHEN 存储文件时 THEN 系统应支持本地存储和云存储（OSS/COS）
3. WHEN 关联文件时 THEN 系统应通过多态关联支持文件与业务对象的关联
4. WHEN 访问文件时 THEN 系统应提供安全的文件访问控制

### 需求9：内容审核系统

**用户故事：** 作为管理员，我需要对用户提交的内容进行审核，以便确保平台内容质量。

#### 验收标准

1. WHEN 内容提交时 THEN 系统应自动触发审核流程
2. WHEN 自动审核时 THEN 系统应根据关键词和规则进行初步筛选
3. WHEN 人工审核时 THEN 系统应提供完整的审核界面和操作记录
4. WHEN 审核完成时 THEN 系统应自动更新内容状态并发放奖励

### 需求10：系统配置与统计

**用户故事：** 作为管理员，我需要管理系统配置和查看运营数据，以便优化平台运营。

#### 验收标准

1. WHEN 配置系统参数时 THEN 系统应提供灵活的配置管理功能
2. WHEN 查看统计数据时 THEN 系统应提供多维度的数据统计报表
3. WHEN 记录操作日志时 THEN 系统应完整记录关键操作的日志信息
4. WHEN 监控系统时 THEN 系统应提供性能监控和异常告警功能

## 技术约束

1. **框架版本**：必须使用Laravel 9.x框架
2. **数据库**：使用MySQL 5.7，无外键约束设计
3. **认证方式**：使用Laravel Sanctum进行API Token认证
4. **响应格式**：必须遵循现有项目的API响应格式规范
5. **代码风格**：必须与现有Laravel项目保持一致的编码风格
6. **模块隔离**：所有新功能必须在独立的citui模块中实现

## 性能要求

1. **响应时间**：API响应时间应控制在500ms以内
2. **并发处理**：支持至少1000个并发用户访问
3. **数据库优化**：合理使用索引，查询性能优化
4. **缓存策略**：对热点数据实施Redis缓存策略

## 安全要求

1. **数据验证**：所有输入数据必须进行严格验证
2. **权限控制**：实施基于角色的访问控制
3. **文件安全**：上传文件必须进行类型和大小限制
4. **SQL注入防护**：使用Laravel ORM防止SQL注入攻击