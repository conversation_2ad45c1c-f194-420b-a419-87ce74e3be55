# Project Structure

This is a monorepo containing two main applications that work together as a full-stack solution.

## Root Structure
```
├── cituiapp/          # Frontend uni-app application
├── cituilaravel/      # Backend Laravel API
└── .kiro/            # Kiro IDE configuration
```

## Frontend Structure (cituiapp/)
```
cituiapp/
├── App.vue                    # Root application component
├── main.js                    # Application entry point
├── pages.json                 # Page routing and tabBar configuration
├── manifest.json              # Multi-platform build configuration
├── uni.scss                   # Global styles
├── pages/                     # Page components
│   ├── index/                 # Home page
│   ├── evaluation/            # Evaluation/assessment pages
│   ├── clue/                  # Clue sharing pages
│   ├── invite/                # Invitation system pages
│   ├── profile/               # User profile pages
│   └── login/                 # Authentication pages
├── components/                # Reusable Vue components
├── static/                    # Static assets (images, icons)
├── common/                    # Shared utilities and constants
├── utils/                     # Helper functions
├── config/                    # Configuration files (request.js, etc.)
└── unpackage/                 # Build output directory
```

## Backend Structure (cituilaravel/)
```
cituilaravel/
├── app/                       # Application logic
├── config/                    # Configuration files
├── routes/                    # API routes
├── resources/                 # Views, assets, language files
├── storage/                   # File storage, logs, cache
├── public/                    # Web server document root
├── database/                  # Migrations, seeders, factories
├── tests/                     # Test files
├── artisan                    # Laravel command-line interface
├── composer.json              # PHP dependencies
├── .env                       # Environment configuration
├── _ide_helper.php            # IDE autocomplete support
└── _ide_helper_models.php     # Model autocomplete support
```

## Key Configuration Files
- **cituiapp/pages.json**: Defines page routes, tabBar, and navigation styles
- **cituiapp/manifest.json**: Multi-platform build settings and app metadata
- **cituiapp/main.js**: Vue app initialization and uView UI setup
- **cituilaravel/.env**: Database and service configurations
- **cituilaravel/composer.json**: PHP dependencies and autoloading

## Naming Conventions
- **Pages**: Kebab-case directory names (e.g., `submit-report/`)
- **Components**: PascalCase for Vue components
- **Assets**: Organized by type in `static/` directory
- **API Routes**: RESTful conventions in Laravel backend
- **Database**: Snake_case for table and column names

## Development Workflow
1. Frontend development in `cituiapp/` using HBuilderX or uni-app CLI
2. Backend API development in `cituilaravel/` using standard Laravel practices
3. Database schema managed through Laravel migrations
4. Multi-platform builds handled through uni-app's conditional compilation