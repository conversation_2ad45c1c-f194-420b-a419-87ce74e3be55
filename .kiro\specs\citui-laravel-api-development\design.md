# CitUI Laravel后端API系统设计文档

## 概述

本设计文档基于需求分析，为CitUI应用设计一个完整的Laravel 9后端API系统。系统采用模块化架构，严格遵循现有项目的代码规范和响应格式，确保与前端uni-app应用的完美集成。

## 系统架构

### 整体架构设计

```
┌─────────────────────────────────────────────────────────────┐
│                    前端层 (uni-app)                          │
├─────────────────────────────────────────────────────────────┤
│                    API网关层                                 │
├─────────────────────────────────────────────────────────────┤
│                 控制器层 (Controllers)                       │
├─────────────────────────────────────────────────────────────┤
│                 服务层 (Services)                           │
├─────────────────────────────────────────────────────────────┤
│                 模型层 (Models)                             │
├─────────────────────────────────────────────────────────────┤
│                 数据层 (MySQL)                      │
└─────────────────────────────────────────────────────────────┘
```

### 模块化设计

基于现有项目结构，创建独立的CitUI模块：

```
app/
├── Http/Controllers/Citui/          # CitUI控制器
│   ├── AuthController.php           # 认证控制器
│   ├── UserController.php           # 用户管理控制器
│   ├── AppController.php            # APP管理控制器
│   ├── EvaluationController.php     # 评测系统控制器
│   ├── ClueController.php           # 线索系统控制器
│   ├── PointController.php          # 积分系统控制器
│   ├── FileController.php           # 文件管理控制器
│   └── AdminController.php          # 管理后台控制器
├── Models/Citui/                    # CitUI模型
│   ├── User.php                     # 用户模型
│   ├── App.php                      # APP模型
│   ├── EvaluationReport.php         # 评测报告模型
│   ├── WaterClue.php                # 线索模型
│   ├── PointRecord.php              # 积分记录模型
│   └── ...                         # 其他模型
├── Services/Citui/                  # CitUI服务层
│   ├── AuthService.php              # 认证服务
│   ├── UserService.php              # 用户服务
│   ├── AppService.php               # APP服务
│   ├── EvaluationService.php        # 评测服务
│   ├── ClueService.php              # 线索服务
│   ├── PointService.php             # 积分服务
│   ├── FileService.php              # 文件服务
│   └── AuditService.php             # 审核服务
└── ...
```

## 数据库设计

### 核心表结构分析

基于提供的SQL文件，系统包含23个核心表，按功能模块分组：

#### 用户相关表
- `ct_admin_users`: 管理员用户表
- `ct_users`: 普通用户表  
- `ct_user_logins`: 用户登录记录表
- `ct_user_relations`: 用户关系表

#### APP相关表
- `ct_app_categories`: APP分类表
- `ct_apps`: APP信息表

#### 评测相关表
- `ct_evaluation_reports`: 评测报告表
- `ct_evaluation_details`: 评测数据详情表

#### 线索相关表
- `ct_water_clues`: 放水线索表
- `ct_clue_feedbacks`: 线索反馈表
- `ct_clue_statistics`: 线索统计表

#### 积分相关表
- `ct_point_rules`: 积分规则表
- `ct_reward_configs`: 奖励配置表
- `ct_point_records`: 积分记录表

#### 文件相关表
- `ct_file_categories`: 文件分类表
- `ct_files`: 文件信息表
- `ct_file_relations`: 文件关联表

#### 审核相关表
- `ct_audit_rules`: 审核规则表
- `ct_content_audits`: 内容审核表
- `ct_audit_logs`: 审核日志表

#### 系统相关表
- `ct_system_configs`: 系统配置表
- `ct_operation_logs`: 操作日志表
- `ct_statistics`: 统计数据表

### 数据库优化建议

1. **索引优化**：为高频查询字段添加复合索引
2. **分区策略**：对大数据量表（如日志表）实施分区
3. **缓存策略**：热点数据使用Redis缓存
4. **读写分离**：考虑主从复制提升查询性能

## API设计规范

### 统一响应格式

基于现有项目的ApiResponse trait，统一API响应格式：

```php
// 成功响应
{
    "status": 200,
    "code": 1,
    "msg": "success",
    "data": {
        // 具体数据
    }
}

// 错误响应
{
    "status": 200,
    "code": 0,
    "msg": "error message",
    "data": []
}

// 分页响应
{
    "status": 200,
    "code": 1,
    "msg": "success",
    "data": {
        "items": [],
        "pagination": {
            "current_page": 1,
            "per_page": 15,
            "total": 100,
            "last_page": 7,
            "has_more": true
        }
    }
}
```

### 路由设计

创建独立的CitUI路由文件：

```php
// routes/citui.php
Route::prefix('citui')->group(function () {
    // 公开接口
    Route::post('auth/register', [AuthController::class, 'register']);
    Route::post('auth/login', [AuthController::class, 'login']);
    Route::get('apps', [AppController::class, 'index']);
    
    // 需要认证的接口
    Route::middleware('auth:sanctum')->group(function () {
        Route::get('user/profile', [UserController::class, 'profile']);
        Route::post('evaluations', [EvaluationController::class, 'store']);
        Route::post('clues', [ClueController::class, 'store']);
        // ... 其他接口
    });
});
```

## 组件和接口设计

### 认证授权组件

#### AuthService设计
```php
class AuthService extends BaseService
{
    public function register(array $data): array
    {
        // 1. 验证手机号和验证码
        // 2. 创建用户记录
        // 3. 发放注册奖励积分
        // 4. 生成API Token
        // 5. 返回用户信息和Token
    }
    
    public function login(array $data): array
    {
        // 1. 验证登录凭据
        // 2. 记录登录日志
        // 3. 生成API Token
        // 4. 返回用户信息和Token
    }
}
```

#### 中间件设计
```php
class CituiAuthMiddleware
{
    public function handle($request, Closure $next)
    {
        // 1. 验证Token有效性
        // 2. 获取用户信息
        // 3. 设置用户上下文
        // 4. 继续请求处理
    }
}
```

### 用户管理组件

#### UserService设计
```php
class UserService extends BaseService
{
    public function updateProfile(int $userId, array $data): array
    {
        // 1. 验证用户权限
        // 2. 验证数据格式
        // 3. 更新用户信息
        // 4. 返回更新结果
    }
    
    public function uploadAvatar(int $userId, UploadedFile $file): array
    {
        // 1. 验证文件类型和大小
        // 2. 生成唯一文件名
        // 3. 上传到存储系统
        // 4. 更新用户头像URL
        // 5. 记录文件关联关系
    }
}
```

### APP管理组件

#### AppService设计
```php
class AppService extends BaseService
{
    public function getAppList(array $filters): array
    {
        // 1. 构建查询条件
        // 2. 执行分页查询
        // 3. 格式化返回数据
        // 4. 记录查看统计
    }
    
    public function getAppDetail(int $appId): array
    {
        // 1. 查询APP详细信息
        // 2. 获取相关评测报告
        // 3. 增加查看次数
        // 4. 返回完整信息
    }
}
```

### 评测系统组件

#### EvaluationService设计
```php
class EvaluationService extends BaseService
{
    public function submitReport(int $userId, array $data): array
    {
        // 1. 验证用户权限和频率限制
        // 2. 保存评测报告
        // 3. 处理截图文件关联
        // 4. 提交内容审核
        // 5. 发放奖励积分
    }
    
    public function getReportList(array $filters): array
    {
        // 1. 构建查询条件
        // 2. 执行分页查询
        // 3. 格式化返回数据
        // 4. 包含用户和APP信息
    }
}
```

### 线索系统组件

#### ClueService设计
```php
class ClueService extends BaseService
{
    public function publishClue(int $userId, array $data): array
    {
        // 1. 验证用户权限
        // 2. 保存线索信息
        // 3. 处理截图文件
        // 4. 提交审核
        // 5. 发放奖励积分
    }
    
    public function submitFeedback(int $userId, int $clueId, array $data): array
    {
        // 1. 验证线索存在性
        // 2. 保存反馈信息
        // 3. 更新线索统计
        // 4. 发放反馈奖励
    }
}
```

### 积分系统组件

#### PointService设计
```php
class PointService extends BaseService
{
    public function awardPoints(int $userId, string $ruleCode, array $context = []): bool
    {
        // 1. 获取积分规则
        // 2. 验证发放条件
        // 3. 计算积分数量
        // 4. 记录积分变化
        // 5. 更新用户积分余额
    }
    
    public function exchangeReward(int $userId, int $configId, array $data): array
    {
        // 1. 验证奖励配置
        // 2. 检查积分余额
        // 3. 扣除积分
        // 4. 记录兑换记录
        // 5. 处理奖励发放
    }
}
```

### 文件管理组件

#### FileService设计
```php
class FileService extends BaseService
{
    public function uploadFile(UploadedFile $file, array $options = []): array
    {
        // 1. 验证文件类型和大小
        // 2. 生成唯一文件名
        // 3. 上传到存储系统
        // 4. 记录文件信息
        // 5. 返回文件URL和ID
    }
    
    public function associateFile(int $fileId, string $businessType, int $businessId): bool
    {
        // 1. 验证文件存在性
        // 2. 创建关联记录
        // 3. 更新关联状态
    }
}
```

### 审核系统组件

#### AuditService设计
```php
class AuditService extends BaseService
{
    public function submitForAudit(string $contentType, int $contentId, int $submitterId): array
    {
        // 1. 获取审核规则
        // 2. 执行自动审核
        // 3. 创建审核记录
        // 4. 分配审核员（如需要）
        // 5. 返回审核状态
    }
    
    public function processAudit(int $auditId, string $result, string $reason = ''): bool
    {
        // 1. 更新审核状态
        // 2. 记录审核日志
        // 3. 更新内容状态
        // 4. 发放奖励或扣分
    }
}
```

## 数据模型设计

### 基础模型类

```php
abstract class BaseCituiModel extends Model
{
    protected $connection = 'mysql';
    
    // 统一的时间戳字段
    const CREATED_AT = 'created_at';
    const UPDATED_AT = 'updated_at';
    
    // 通用的查询作用域
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }
    
    // 统一的数据格式化
    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format('Y-m-d H:i:s');
    }
}
```

### 用户模型

```php
class User extends BaseCituiModel
{
    protected $table = 'ct_users';
    protected $primaryKey = 'user_id';
    
    protected $fillable = [
        'phone', 'nickname', 'real_name', 'avatar_url',
        'gender', 'birthday', 'province', 'city'
    ];
    
    protected $hidden = ['password_hash'];
    
    // 关联关系
    public function evaluationReports()
    {
        return $this->hasMany(EvaluationReport::class, 'user_id', 'user_id');
    }
    
    public function waterClues()
    {
        return $this->hasMany(WaterClue::class, 'user_id', 'user_id');
    }
    
    public function pointRecords()
    {
        return $this->hasMany(PointRecord::class, 'user_id', 'user_id');
    }
}
```

## 错误处理设计

### 统一异常处理

```php
class CituiExceptionHandler
{
    public function handle(Exception $exception)
    {
        // 1. 记录错误日志
        // 2. 格式化错误响应
        // 3. 返回统一格式的错误信息
        
        if ($exception instanceof ValidationException) {
            return response()->json([
                'status' => 422,
                'code' => 0,
                'msg' => '数据验证失败',
                'data' => $exception->errors()
            ], 422);
        }
        
        // 其他异常处理...
    }
}
```

### 自定义异常类

```php
class CituiBusinessException extends Exception
{
    protected $errorCode;
    
    public function __construct($message, $errorCode = 0)
    {
        parent::__construct($message);
        $this->errorCode = $errorCode;
    }
    
    public function getErrorCode()
    {
        return $this->errorCode;
    }
}
```

## 测试策略

### 单元测试

```php
class UserServiceTest extends TestCase
{
    public function testUserRegistration()
    {
        // 1. 准备测试数据
        // 2. 调用注册方法
        // 3. 验证返回结果
        // 4. 检查数据库记录
        // 5. 验证积分发放
    }
    
    public function testUserLogin()
    {
        // 测试用户登录功能
    }
}
```

### 集成测试

```php
class EvaluationApiTest extends TestCase
{
    public function testSubmitEvaluationReport()
    {
        // 1. 创建测试用户
        // 2. 模拟文件上传
        // 3. 提交评测报告
        // 4. 验证API响应
        // 5. 检查审核流程
    }
}
```

## 性能优化策略

### 数据库优化

1. **查询优化**：使用合适的索引，避免N+1查询
2. **连接池**：配置数据库连接池
3. **读写分离**：配置主从数据库
4. **分页优化**：使用游标分页替代偏移分页

### 缓存策略

```php
class CacheService
{
    public function getAppList($cacheKey, $callback, $ttl = 3600)
    {
        return Cache::remember($cacheKey, $ttl, $callback);
    }
    
    public function invalidateAppCache($appId)
    {
        Cache::forget("app_detail_{$appId}");
        Cache::forget("app_list_*");
    }
}
```

### 队列处理

```php
class ProcessAuditJob implements ShouldQueue
{
    public function handle()
    {
        // 异步处理审核任务
        // 1. 执行自动审核
        // 2. 发送审核通知
        // 3. 更新统计数据
    }
}
```

## 安全设计

### 数据验证

```php
class EvaluationReportRequest extends FormRequest
{
    public function rules()
    {
        return [
            'app_id' => 'required|integer|exists:ct_apps,app_id',
            'report_title' => 'required|string|max:200',
            'report_content' => 'required|string|max:5000',
            'rating' => 'required|integer|between:1,5',
            'screenshots' => 'array|max:5',
            'screenshots.*' => 'image|max:2048'
        ];
    }
}
```

### 权限控制

```php
class CituiPolicy
{
    public function update(User $user, EvaluationReport $report)
    {
        return $user->user_id === $report->user_id;
    }
    
    public function delete(User $user, EvaluationReport $report)
    {
        return $user->user_id === $report->user_id 
            && $report->status === 'draft';
    }
}
```

### 文件安全

```php
class FileUploadService
{
    protected $allowedTypes = ['jpg', 'jpeg', 'png', 'webp'];
    protected $maxSize = 2 * 1024 * 1024; // 2MB
    
    public function validateFile(UploadedFile $file)
    {
        // 1. 检查文件类型
        // 2. 检查文件大小
        // 3. 检查文件内容
        // 4. 防止恶意文件上传
    }
}
```

## 监控和日志

### 操作日志

```php
class OperationLogger
{
    public function log($action, $userId, $data = [])
    {
        OperationLog::create([
            'user_id' => $userId,
            'action' => $action,
            'data' => json_encode($data),
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'created_at' => now()
        ]);
    }
}
```

### 性能监控

```php
class PerformanceMonitor
{
    public function trackApiResponse($route, $duration, $memory)
    {
        // 记录API响应时间和内存使用
        // 发送到监控系统
    }
}
```

这个设计文档提供了完整的系统架构、组件设计和实现策略，确保CitUI Laravel后端API系统的高质量开发和部署。