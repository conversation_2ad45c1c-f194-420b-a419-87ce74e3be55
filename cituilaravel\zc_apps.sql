CREATE TABLE `zc_apps` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `category_id` int(11) unsigned NOT NULL COMMENT '分类ID',
  `app_name` varchar(200) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '应用名称',
  `app_package` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '应用包名',
  `app_version` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '应用版本',
  `developer` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '开发商',
  `app_label` varchar(1000) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '标签',
  `app_size` bigint(20) DEFAULT NULL COMMENT '应用大小(字节)',
  `download_url` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '下载链接',
  `logo_url` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Logo图片URL',
  `description` text COLLATE utf8mb4_unicode_ci COMMENT '应用描述',
  `features` json DEFAULT NULL COMMENT '应用特性(JSON格式)',
  `screenshots` json DEFAULT NULL COMMENT '应用截图(JSON格式)',
  `rating` decimal(3,2) DEFAULT '0.00' COMMENT '平均评分',
  `rating_count` int(11) DEFAULT '0' COMMENT '评分人数',
  `download_count` bigint(20) DEFAULT '0' COMMENT '下载次数',
  `view_count` bigint(20) DEFAULT '0' COMMENT '查看次数',
  `status` enum('active','inactive','pending') COLLATE utf8mb4_unicode_ci DEFAULT 'pending' COMMENT '应用状态',
  `is_featured` tinyint(1) DEFAULT '0' COMMENT '是否推荐',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_category_id` (`category_id`) USING BTREE,
  KEY `idx_app_name` (`app_name`) USING BTREE,
  KEY `idx_app_package` (`app_package`) USING BTREE,
  KEY `idx_rating` (`rating`) USING BTREE,
  KEY `idx_download_count` (`download_count`) USING BTREE,
  KEY `idx_view_count` (`view_count`) USING BTREE,
  KEY `idx_status` (`status`) USING BTREE,
  KEY `idx_is_featured` (`is_featured`) USING BTREE,
  KEY `idx_created_at` (`created_at`) USING BTREE,
  KEY `idx_category_status` (`category_id`,`status`) USING BTREE,
  KEY `idx_status_featured` (`status`,`is_featured`) USING BTREE,
  KEY `idx_status_rating` (`status`,`rating`) USING BTREE,
  KEY `idx_status_downloads` (`status`,`download_count`) USING BTREE,
  KEY `idx_featured_rating` (`is_featured`,`rating`) USING BTREE,
  FULLTEXT KEY `ft_app_name_desc` (`app_name`,`description`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='APP信息表';

CREATE TABLE `zc_evaluation_reports` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '报告ID',
  `app_id` bigint(20) unsigned NOT NULL COMMENT '应用ID',
  `user_id` bigint(20) unsigned NOT NULL COMMENT '用户ID',
  `report_title` varchar(200) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '报告标题',
  `report_content` text COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '报告内容',
  `download_url` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '下载地址',
  `pingfen` smallint(3) unsigned DEFAULT '1' COMMENT '评分(1-5)',
  `yunxingmoshi` tinyint(3) unsigned DEFAULT '2' COMMENT '运行模式 1自动  2手动',
  `xinrenfuli` decimal(10,2) unsigned DEFAULT NULL COMMENT '新人福利',
  `tixianmenkan` decimal(10,2) unsigned DEFAULT '0.00' COMMENT '提现门槛',
  `dingbaojine` decimal(10,2) unsigned DEFAULT '0.00' COMMENT '顶包金额',
  `ceshitiaoshu` smallint(5) unsigned DEFAULT '1' COMMENT '测试条数',
  `ceshishouyi` decimal(10,2) unsigned DEFAULT '0.00' COMMENT '测试总收益',
  `ceshishichang` decimal(10,2) unsigned DEFAULT '0.00' COMMENT '测试时长',
  `ceshishebei` varchar(150) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '测试设备',
  `cepingren` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT '0' COMMENT '测评人',
  `status` tinyint(3) unsigned DEFAULT '1' COMMENT '状态',
  `view_count` int(11) unsigned DEFAULT '0' COMMENT '查看次数',
  `like_count` int(11) unsigned DEFAULT '0' COMMENT '点赞次数',
  `is_featured` tinyint(1) unsigned DEFAULT '0' COMMENT '是否推荐',
  `cepingriqi` date DEFAULT NULL COMMENT '测试日期',
  `shouyi_1` decimal(10,2) unsigned DEFAULT '0.00' COMMENT '第一条价格',
  `shouyi_2` decimal(10,2) unsigned DEFAULT '0.00' COMMENT '第二条价格',
  `shouyi_3` decimal(10,2) unsigned DEFAULT '0.00' COMMENT '第三条价格',
  `shouyi_4` decimal(10,2) unsigned DEFAULT '0.00' COMMENT '第四条价格',
  `shouyi_5` decimal(10,2) unsigned DEFAULT '0.00' COMMENT '第五条价格',
  `pic_main` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '主图',
  `pic_tixian` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '提现记录截图',
  `pic_daozhang` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '到账记录截图',
  `submitted_at` timestamp NULL DEFAULT NULL COMMENT '提交时间',
  `approved_at` timestamp NULL DEFAULT NULL COMMENT '审核通过时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_app_id` (`app_id`) USING BTREE,
  KEY `idx_user_id` (`user_id`) USING BTREE,
  KEY `idx_rating` (`dingbaojine`) USING BTREE,
  KEY `idx_status` (`status`) USING BTREE,
  KEY `idx_is_featured` (`is_featured`) USING BTREE,
  KEY `idx_submitted_at` (`submitted_at`) USING BTREE,
  KEY `idx_approved_at` (`approved_at`) USING BTREE,
  KEY `idx_created_at` (`created_at`) USING BTREE,
  KEY `idx_view_count` (`view_count`) USING BTREE,
  KEY `idx_like_count` (`like_count`) USING BTREE,
  KEY `idx_app_user` (`app_id`,`user_id`) USING BTREE,
  KEY `idx_status_submitted` (`status`,`submitted_at`) USING BTREE,
  KEY `idx_status_featured` (`status`,`is_featured`) USING BTREE,
  KEY `idx_app_status` (`app_id`,`status`) USING BTREE,
  KEY `idx_user_status` (`user_id`,`status`) USING BTREE,
  KEY `idx_difficulty_rating` (`tixianmenkan`,`dingbaojine`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='评测报告表';