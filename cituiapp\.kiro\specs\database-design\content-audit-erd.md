# 内容审核系统实体关系图

## Mermaid ERD 图表

```mermaid
erDiagram
    ct_audit_rules {
        int rule_id PK "规则ID"
        varchar rule_name "规则名称"
        varchar rule_code UK "规则代码"
        varchar content_type "内容类型"
        text rule_description "规则描述"
        tinyint auto_audit_enabled "启用自动审核"
        text auto_audit_keywords "自动审核关键词"
        tinyint manual_audit_required "需要人工审核"
        int audit_timeout_hours "审核超时时间"
        int pass_score_threshold "通过分数阈值"
        int reject_score_threshold "拒绝分数阈值"
        int reward_points "奖励积分"
        int penalty_points "惩罚积分"
        tinyint is_active "是否启用"
        int sort_order "排序顺序"
        bigint created_by FK "创建人ID"
        timestamp created_at "创建时间"
        timestamp updated_at "更新时间"
    }

    ct_content_audits {
        bigint audit_id PK "审核ID"
        int rule_id FK "审核规则ID"
        varchar content_type "内容类型"
        bigint content_id "内容对象ID"
        bigint submitter_id FK "提交者ID"
        enum audit_status "审核状态"
        int auto_audit_score "自动审核分数"
        enum auto_audit_result "自动审核结果"
        text auto_audit_reason "自动审核原因"
        tinyint manual_audit_required "需要人工审核"
        bigint auditor_id FK "审核员ID"
        enum audit_result "最终审核结果"
        text audit_reason "审核原因"
        int audit_score "人工审核分数"
        varchar reject_category "拒绝分类"
        json content_snapshot "内容快照"
        enum priority_level "优先级"
        timestamp submitted_at "提交时间"
        timestamp audit_started_at "开始审核时间"
        timestamp audit_completed_at "完成审核时间"
        timestamp timeout_at "超时时间"
        timestamp created_at "创建时间"
        timestamp updated_at "更新时间"
    }

    ct_audit_logs {
        bigint log_id PK "日志ID"
        bigint audit_id FK "审核ID"
        bigint operator_id FK "操作人ID"
        enum operator_type "操作人类型"
        varchar action_type "操作类型"
        varchar old_status "原状态"
        varchar new_status "新状态"
        text action_reason "操作原因"
        json action_data "操作数据"
        varchar ip_address "操作IP"
        varchar user_agent "用户代理"
        int processing_time "处理耗时"
        timestamp created_at "创建时间"
    }

    ct_admin_users {
        bigint admin_id PK "管理员ID"
        varchar username "用户名"
        varchar real_name "真实姓名"
        varchar email "邮箱"
        varchar phone "手机号"
        timestamp created_at "创建时间"
    }

    ct_users {
        bigint user_id PK "用户ID"
        varchar phone "手机号"
        varchar nickname "昵称"
        varchar avatar_url "头像URL"
        timestamp created_at "创建时间"
    }

    ct_evaluation_reports {
        bigint report_id PK "报告ID"
        bigint app_id FK "应用ID"
        bigint user_id FK "用户ID"
        varchar report_title "报告标题"
        text report_content "报告内容"
        timestamp created_at "创建时间"
    }

    ct_water_clues {
        bigint clue_id PK "线索ID"
        bigint app_id FK "应用ID"
        bigint user_id FK "用户ID"
        varchar clue_title "线索标题"
        text clue_content "线索内容"
        timestamp created_at "创建时间"
    }

    ct_apps {
        bigint app_id PK "应用ID"
        varchar app_name "应用名称"
        varchar logo_url "Logo URL"
        timestamp created_at "创建时间"
    }

    %% 关系定义
    ct_admin_users ||--o{ ct_audit_rules : "created_by"
    ct_audit_rules ||--o{ ct_content_audits : "rule_id"
    ct_users ||--o{ ct_content_audits : "submitter_id"
    ct_admin_users ||--o{ ct_content_audits : "auditor_id"
    ct_content_audits ||--o{ ct_audit_logs : "audit_id"
    ct_admin_users ||--o{ ct_audit_logs : "operator_id"
    
    %% 业务对象关联（通过content_audits表的多态关联）
    ct_evaluation_reports ||--o{ ct_content_audits : "content_id (evaluation_report)"
    ct_water_clues ||--o{ ct_content_audits : "content_id (water_clue)"
    ct_users ||--o{ ct_content_audits : "content_id (user_profile)"
    ct_apps ||--o{ ct_content_audits : "content_id (app_logo)"
```

## 关系说明

### 1. 审核规则管理关系
- **ct_audit_rules** 由 **ct_admin_users** 创建和管理
- 每个审核规则可以应用于多个审核记录
- 关系类型：一对多（1:N）

### 2. 审核记录核心关系
- **ct_content_audits** 基于 **ct_audit_rules** 进行审核
- **ct_content_audits** 关联 **ct_users** 作为内容提交者
- **ct_content_audits** 关联 **ct_admin_users** 作为审核员
- 关系类型：多对一（N:1）

### 3. 审核日志追踪关系
- **ct_audit_logs** 记录 **ct_content_audits** 的所有操作
- **ct_audit_logs** 关联 **ct_admin_users** 作为操作人
- 关系类型：一对多（1:N），支持级联删除

### 4. 多态业务对象关联
通过 `ct_content_audits` 表的多态关联实现：

#### 评测报告审核
- `content_type = 'evaluation_report'`
- `content_id` 指向 `ct_evaluation_reports.report_id`
- 审核评测报告的内容质量和真实性

#### 放水线索审核
- `content_type = 'water_clue'`
- `content_id` 指向 `ct_water_clues.clue_id`
- 审核线索信息的可靠性和合规性

#### 用户资料审核
- `content_type = 'user_profile'`
- `content_id` 指向 `ct_users.user_id`
- 审核用户注册信息和资料更新

#### APP图标审核
- `content_type = 'app_logo'`
- `content_id` 指向 `ct_apps.app_id`
- 审核APP图标的合规性和质量

#### 用户头像审核
- `content_type = 'user_avatar'`
- `content_id` 指向 `ct_users.user_id`
- 审核用户头像的合规性

## 审核状态流转图

```mermaid
stateDiagram-v2
    [*] --> pending : 内容提交
    pending --> reviewing : 分配审核员
    pending --> passed : 自动审核通过
    pending --> rejected : 自动审核拒绝
    reviewing --> passed : 人工审核通过
    reviewing --> rejected : 人工审核拒绝
    reviewing --> timeout : 审核超时
    timeout --> reviewing : 重新分配
    passed --> [*] : 审核完成
    rejected --> [*] : 审核完成
    
    note right of pending
        初始状态
        等待审核
    end note
    
    note right of reviewing
        人工审核中
        已分配审核员
    end note
    
    note right of passed
        审核通过
        内容可见
    end note
    
    note right of rejected
        审核拒绝
        内容不可见
    end note
    
    note right of timeout
        审核超时
        需要处理
    end note
```

## 索引策略

### 主键索引
- 所有表都有自增主键，提供唯一标识和高效查询

### 外键索引
- `ct_content_audits.rule_id` → `ct_audit_rules.rule_id`
- `ct_content_audits.submitter_id` → `ct_users.user_id`
- `ct_content_audits.auditor_id` → `ct_admin_users.admin_id`
- `ct_audit_logs.audit_id` → `ct_content_audits.audit_id`

### 业务查询索引
- `ct_audit_rules.rule_code`：规则代码查询
- `ct_content_audits.audit_status`：审核状态查询
- `ct_content_audits.content_type`：内容类型查询
- `ct_audit_logs.action_type`：操作类型查询

### 复合索引
- `(content_type, content_id)`：业务对象审核查询
- `(audit_status, priority_level)`：优先级审核队列
- `(auditor_id, audit_status)`：审核员工作台
- `(submitter_id, audit_status)`：用户审核状态

### 时间索引
- `ct_content_audits.submitted_at`：提交时间查询
- `ct_content_audits.timeout_at`：超时检查
- `ct_audit_logs.created_at`：日志时间查询

## 数据完整性约束

### 外键约束
1. **级联删除**：审核记录删除时自动清理日志
2. **限制删除**：有审核记录的规则不能被删除
3. **设置NULL**：管理员删除时相关记录设为NULL

### 唯一性约束
1. **规则代码唯一**：`ct_audit_rules.rule_code`
2. **内容审核唯一**：`(content_type, content_id)`

### 检查约束
1. **状态枚举**：audit_status 只能是预定义的枚举值
2. **评分范围**：审核分数在合理范围内
3. **时间逻辑**：完成时间不能早于开始时间

## 触发器机制

### 状态变更触发器
- 自动记录状态变更到审核日志
- 设置审核开始和完成时间戳
- 触发相关业务逻辑（如积分处理）

### 超时设置触发器
- 根据审核规则自动设置超时时间
- 支持不同内容类型的差异化配置

### 日志记录触发器
- 自动记录审核提交日志
- 确保所有操作都有完整的审计追踪

## 查询模式

### 常用查询场景
1. **获取待审核内容列表**
2. **查询特定内容的审核状态**
3. **获取审核员的工作队列**
4. **统计审核效率和质量**
5. **查询审核历史和日志**

### 性能优化建议
1. 使用复合索引优化多条件查询
2. 合理使用分页避免大结果集
3. 预加载关联数据减少N+1查询
4. 使用缓存提升热点数据访问速度
5. 定期归档历史数据保持表大小合理

## 扩展性考虑

### 水平扩展
- 支持按内容类型分片
- 审核日志表可按时间分区
- 支持读写分离架构

### 功能扩展
- 支持新的内容类型审核
- 集成机器学习审核模型
- 支持多级审核流程
- 集成第三方审核服务

### 性能扩展
- 支持异步审核处理
- 集成消息队列系统
- 支持分布式审核架构
- 优化大数据量查询性能