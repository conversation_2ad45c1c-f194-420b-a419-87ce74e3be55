-- =============================================
-- 放水线索相关表结构测试脚本
-- 创建时间: 2025-01-08
-- 描述: 测试放水线索表、反馈表和统计表的功能完整性
-- =============================================

-- 设置测试环境
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 1;

-- =============================================
-- 1. 基础数据准备
-- =============================================

-- 插入测试用户数据
INSERT INTO `ct_users` (`id`, `phone`, `nick_name`, `avatar`, `status`, `total_points`, `available_points`, `created_at`) VALUES
(1001, '13800138001', '测试用户1', 'https://example.com/avatar1.jpg', 1, 100, 50, '2025-01-01 10:00:00'),
(1002, '13800138002', '测试用户2', 'https://example.com/avatar2.jpg', 1, 200, 100, '2025-01-01 11:00:00'),
(1003, '13800138003', '测试用户3', 'https://example.com/avatar3.jpg', 1, 150, 75, '2025-01-01 12:00:00');

-- 插入测试管理员数据
INSERT INTO `ct_admin_users` (`id`, `username`, `password`, `real_name`, `role_type`, `status`, `created_at`) VALUES
(2001, 'admin_test1', MD5('password123'), '测试管理员1', 1, 1, '2025-01-01 09:00:00'),
(2002, 'admin_test2', MD5('password123'), '测试管理员2', 2, 1, '2025-01-01 09:30:00');

-- 插入测试APP分类数据
INSERT INTO `ct_app_categories` (`id`, `name`, `description`, `status`, `create_time`) VALUES
(3001, '任务赚钱', '通过完成任务获得收益的APP', 1, '2025-01-01 08:00:00'),
(3002, '看视频赚钱', '通过观看视频获得收益的APP', 1, '2025-01-01 08:30:00'),
(3003, '走路赚钱', '通过运动步数获得收益的APP', 1, '2025-01-01 09:00:00');

-- 插入测试APP数据
INSERT INTO `ct_apps` (`id`, `name`, `category_id`, `logo_url`, `description`, `rating`, `status`, `create_time`) VALUES
(4001, '任务多多', 3001, 'https://example.com/logo1.jpg', '专业的任务赚钱平台', 4.5, 1, '2025-01-01 10:00:00'),
(4002, '视频赚', 3002, 'https://example.com/logo2.jpg', '看视频就能赚钱', 4.2, 1, '2025-01-01 10:30:00'),
(4003, '步步赚', 3003, 'https://example.com/logo3.jpg', '走路就能赚钱', 4.0, 1, '2025-01-01 11:00:00');

-- =============================================
-- 2. 放水线索表测试
-- =============================================

-- 测试插入放水线索数据
INSERT INTO `ct_water_clues` (
    `app_id`, `user_id`, `title`, `water_amount`, `water_type`, `description`, 
    `water_time`, `duration_minutes`, `participant_count`, `difficulty_level`,
    `device_requirement`, `network_requirement`, `submitter_device`, `submitter_ip`,
    `audit_status`, `status`, `created_at`
) VALUES
(4001, 1001, '任务多多新人福利放水', 50.00, 1, '新用户注册即可获得50元现金奖励，需要完成实名认证', 
 '2025-01-08 14:00:00', 30, 0, 1, 'iOS/Android', 'WiFi/4G', 'iPhone 15 Pro', '*************', 0, 1, NOW()),

(4002, 1002, '视频赚周末活动', 20.00, 2, '周末看视频双倍奖励，每天最多可获得20元', 
 '2025-01-11 09:00:00', 120, 0, 2, 'Android', 'WiFi', 'Xiaomi 14', '*************', 0, 1, NOW()),

(4003, 1003, '步步赚日常任务', 10.00, 3, '每日走路10000步即可获得10元奖励', 
 '2025-01-08 00:00:00', 1440, 0, 1, 'iOS/Android', '4G/5G', 'iPhone 14', '*************', 0, 1, NOW());

-- 测试查询功能
SELECT '=== 测试放水线索基础查询 ===' as test_name;
SELECT id, title, water_amount, water_type, audit_status, created_at 
FROM ct_water_clues 
ORDER BY created_at DESC;

-- 测试按APP筛选
SELECT '=== 测试按APP筛选线索 ===' as test_name;
SELECT wc.id, wc.title, a.name as app_name, wc.water_amount
FROM ct_water_clues wc
JOIN ct_apps a ON wc.app_id = a.id
WHERE a.name = '任务多多';

-- 测试按金额排序
SELECT '=== 测试按金额排序 ===' as test_name;
SELECT id, title, water_amount, water_type
FROM ct_water_clues 
WHERE status = 1
ORDER BY water_amount DESC;

-- 测试管理员审核线索
UPDATE ct_water_clues SET 
    audit_status = 1, 
    audit_user_id = 2001, 
    audit_time = NOW(),
    audit_remark = '线索内容真实有效，审核通过'
WHERE id = (SELECT id FROM (SELECT id FROM ct_water_clues LIMIT 1) as temp);

SELECT '=== 测试审核状态更新 ===' as test_name;
SELECT id, title, audit_status, audit_user_id, audit_time, audit_remark
FROM ct_water_clues 
WHERE audit_status = 1;

-- =============================================
-- 3. 线索反馈表测试
-- =============================================

-- 获取已审核通过的线索ID用于测试
SET @test_clue_id = (SELECT id FROM ct_water_clues WHERE audit_status = 1 LIMIT 1);

-- 测试插入反馈数据
INSERT INTO `ct_clue_feedbacks` (
    `clue_id`, `user_id`, `feedback_type`, `is_successful`, `actual_amount`, 
    `time_spent`, `difficulty_rating`, `accuracy_rating`, `overall_rating`,
    `feedback_content`, `device_info`, `network_info`, `ip_address`, `status`, `created_at`
) VALUES
(@test_clue_id, 1002, 1, 1, 50.00, 25, 2, 5, 5, 
 '按照线索操作，成功获得50元奖励，操作简单，推荐！', 'Xiaomi 14', 'WiFi', '*************', 1, NOW()),

(@test_clue_id, 1003, 1, 1, 48.00, 30, 2, 4, 4, 
 '成功获得奖励，但实际金额比预期少2元，可能是平台扣费', 'iPhone 14', '4G', '*************', 1, NOW()),

(@test_clue_id, 1001, 2, 0, 0.00, 15, 3, 2, 2, 
 '按照步骤操作但没有获得奖励，可能是活动已结束', 'iPhone 15 Pro', 'WiFi', '*************', 1, NOW());

-- 测试反馈查询
SELECT '=== 测试线索反馈查询 ===' as test_name;
SELECT cf.id, cf.clue_id, u.nick_name, cf.feedback_type, cf.is_successful, 
       cf.actual_amount, cf.overall_rating, cf.created_at
FROM ct_clue_feedbacks cf
JOIN ct_users u ON cf.user_id = u.id
ORDER BY cf.created_at DESC;

-- 测试按线索统计反馈
SELECT '=== 测试按线索统计反馈 ===' as test_name;
SELECT 
    clue_id,
    COUNT(*) as total_feedbacks,
    SUM(CASE WHEN is_successful = 1 THEN 1 ELSE 0 END) as success_count,
    SUM(CASE WHEN is_successful = 0 THEN 1 ELSE 0 END) as failed_count,
    ROUND(AVG(CASE WHEN is_successful = 1 THEN actual_amount END), 2) as avg_amount,
    ROUND(AVG(overall_rating), 2) as avg_rating
FROM ct_clue_feedbacks 
WHERE status = 1
GROUP BY clue_id;

-- 测试反馈审核
UPDATE ct_clue_feedbacks SET 
    audit_status = 1, 
    audit_user_id = 2002, 
    audit_time = NOW(),
    audit_remark = '反馈内容真实，审核通过'
WHERE status = 1;

-- =============================================
-- 4. 线索统计表测试
-- =============================================

-- 测试插入统计数据
INSERT INTO `ct_clue_statistics` (
    `clue_id`, `stat_date`, `stat_type`, `view_count`, `like_count`, 
    `feedback_count`, `success_feedback_count`, `failed_feedback_count`,
    `success_rate`, `average_rating`, `created_at`
) VALUES
(@test_clue_id, '2025-01-08', 1, 150, 25, 3, 2, 1, 66.67, 3.67, NOW()),
(@test_clue_id, '2025-01-07', 1, 120, 20, 2, 1, 1, 50.00, 3.50, NOW()),
(@test_clue_id, '2025-01-06', 1, 100, 15, 1, 1, 0, 100.00, 5.00, NOW());

-- 测试统计数据查询
SELECT '=== 测试线索统计数据 ===' as test_name;
SELECT stat_date, view_count, like_count, feedback_count, 
       success_feedback_count, success_rate, average_rating
FROM ct_clue_statistics 
WHERE clue_id = @test_clue_id
ORDER BY stat_date DESC;

-- 测试统计数据聚合
SELECT '=== 测试统计数据聚合 ===' as test_name;
SELECT 
    clue_id,
    SUM(view_count) as total_views,
    SUM(like_count) as total_likes,
    SUM(feedback_count) as total_feedbacks,
    ROUND(AVG(success_rate), 2) as avg_success_rate,
    ROUND(AVG(average_rating), 2) as avg_rating
FROM ct_clue_statistics 
GROUP BY clue_id;

-- =============================================
-- 5. 触发器功能测试
-- =============================================

-- 测试查看次数更新触发器
SELECT '=== 测试触发器：更新查看次数 ===' as test_name;
UPDATE ct_water_clues SET view_count = view_count + 10 WHERE id = @test_clue_id;

-- 检查统计表是否自动更新
SELECT stat_date, view_count, updated_at 
FROM ct_clue_statistics 
WHERE clue_id = @test_clue_id AND stat_date = CURDATE();

-- 测试反馈插入触发器
SELECT '=== 测试触发器：插入新反馈 ===' as test_name;
INSERT INTO `ct_clue_feedbacks` (
    `clue_id`, `user_id`, `feedback_type`, `is_successful`, `actual_amount`, 
    `overall_rating`, `feedback_content`, `status`, `created_at`
) VALUES
(@test_clue_id, 1001, 1, 1, 45.00, 4, '第二次尝试成功了！', 1, NOW());

-- 检查线索表统计字段是否自动更新
SELECT id, comment_count, verification_count, success_verification_count
FROM ct_water_clues 
WHERE id = @test_clue_id;

-- 检查统计表是否自动更新
SELECT stat_date, feedback_count, success_feedback_count, updated_at
FROM ct_clue_statistics 
WHERE clue_id = @test_clue_id AND stat_date = CURDATE();

-- =============================================
-- 6. 复杂查询测试
-- =============================================

-- 测试热门线索查询（综合排序）
SELECT '=== 测试热门线索查询 ===' as test_name;
SELECT 
    wc.id, wc.title, a.name as app_name, wc.water_amount,
    wc.view_count, wc.like_count, wc.comment_count,
    ROUND((wc.view_count * 0.3 + wc.like_count * 0.5 + wc.comment_count * 0.2), 2) as hot_score
FROM ct_water_clues wc
JOIN ct_apps a ON wc.app_id = a.id
WHERE wc.status = 1 AND wc.audit_status = 1
ORDER BY hot_score DESC, wc.water_amount DESC
LIMIT 10;

-- 测试用户活跃度统计
SELECT '=== 测试用户活跃度统计 ===' as test_name;
SELECT 
    u.id, u.nick_name,
    COUNT(DISTINCT wc.id) as clues_submitted,
    COUNT(DISTINCT cf.id) as feedbacks_given,
    ROUND(AVG(cf.overall_rating), 2) as avg_rating_given
FROM ct_users u
LEFT JOIN ct_water_clues wc ON u.id = wc.user_id
LEFT JOIN ct_clue_feedbacks cf ON u.id = cf.user_id
GROUP BY u.id, u.nick_name
HAVING clues_submitted > 0 OR feedbacks_given > 0
ORDER BY clues_submitted DESC, feedbacks_given DESC;

-- 测试APP线索质量分析
SELECT '=== 测试APP线索质量分析 ===' as test_name;
SELECT 
    a.id, a.name as app_name,
    COUNT(DISTINCT wc.id) as total_clues,
    COUNT(DISTINCT cf.id) as total_feedbacks,
    ROUND(AVG(CASE WHEN cf.is_successful = 1 THEN 1.0 ELSE 0.0 END) * 100, 2) as success_rate,
    ROUND(AVG(cf.overall_rating), 2) as avg_rating,
    ROUND(AVG(wc.water_amount), 2) as avg_amount
FROM ct_apps a
LEFT JOIN ct_water_clues wc ON a.id = wc.app_id AND wc.status = 1
LEFT JOIN ct_clue_feedbacks cf ON wc.id = cf.clue_id AND cf.status = 1
GROUP BY a.id, a.name
HAVING total_clues > 0
ORDER BY success_rate DESC, avg_rating DESC;

-- =============================================
-- 7. 性能测试查询
-- =============================================

-- 测试索引使用情况
SELECT '=== 测试索引使用情况 ===' as test_name;
EXPLAIN SELECT * FROM ct_water_clues WHERE app_id = 4001 AND status = 1 ORDER BY water_amount DESC;
EXPLAIN SELECT * FROM ct_clue_feedbacks WHERE clue_id = @test_clue_id AND is_successful = 1;
EXPLAIN SELECT * FROM ct_clue_statistics WHERE stat_date >= '2025-01-01' AND stat_type = 1;

-- =============================================
-- 8. 数据完整性测试
-- =============================================

-- 测试外键约束
SELECT '=== 测试外键约束 ===' as test_name;

-- 尝试插入不存在的APP ID（应该失败）
-- INSERT INTO ct_water_clues (app_id, user_id, title, water_amount, description, water_time) 
-- VALUES (9999, 1001, '测试线索', 10.00, '测试描述', NOW());

-- 尝试插入不存在的用户ID（应该失败）
-- INSERT INTO ct_water_clues (app_id, user_id, title, water_amount, description, water_time) 
-- VALUES (4001, 9999, '测试线索', 10.00, '测试描述', NOW());

-- 测试级联删除（注释掉以避免删除测试数据）
-- DELETE FROM ct_users WHERE id = 1001;

-- =============================================
-- 9. 清理测试数据
-- =============================================

-- 注释掉清理语句，保留测试数据用于进一步验证
/*
DELETE FROM ct_clue_statistics WHERE clue_id IN (SELECT id FROM ct_water_clues WHERE user_id IN (1001, 1002, 1003));
DELETE FROM ct_clue_feedbacks WHERE user_id IN (1001, 1002, 1003);
DELETE FROM ct_water_clues WHERE user_id IN (1001, 1002, 1003);
DELETE FROM ct_apps WHERE id IN (4001, 4002, 4003);
DELETE FROM ct_app_categories WHERE id IN (3001, 3002, 3003);
DELETE FROM ct_admin_users WHERE id IN (2001, 2002);
DELETE FROM ct_users WHERE id IN (1001, 1002, 1003);
*/

SELECT '=== 放水线索相关表结构测试完成 ===' as test_result;
SELECT '所有测试用例执行完毕，请检查上述查询结果验证功能正确性' as message;