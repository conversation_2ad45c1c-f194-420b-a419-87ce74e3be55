# 系统配置和统计表结构设计文档

## 概述

系统配置和统计模块是次推应用的核心基础设施，负责系统参数的动态配置管理、重要操作的审计日志记录，以及多维度的数据统计分析。该模块为系统的运营管理、性能监控和数据分析提供强有力的支撑。

## 业务需求分析

### 核心功能需求
1. **系统配置管理**：支持积分规则、APP分类、审核规则等系统参数的动态配置
2. **操作日志记录**：记录所有重要操作的详细日志，支持审计和问题追踪
3. **数据统计分析**：提供用户活跃度、内容提交量、积分发放等多维度统计
4. **配置变更追踪**：记录配置修改历史，支持版本管理和回滚
5. **实时监控支持**：为系统监控和告警提供数据基础

### 业务场景
- **运营配置调整**：管理员调整积分奖励、审核规则等运营参数
- **系统行为监控**：追踪用户操作、系统异常和性能指标
- **数据分析报表**：生成用户活跃度、内容质量、收益分析等报表
- **安全审计**：记录敏感操作，支持安全审计和合规检查
- **性能优化**：基于统计数据进行系统性能分析和优化

## 表结构设计

### 1. 系统配置表 (ct_system_configs)

#### 设计目标
- 提供灵活的系统参数配置管理
- 支持不同数据类型的配置值
- 实现配置的分组管理和权限控制
- 支持配置版本管理和变更追踪

#### 核心字段
- `config_key`: 配置键名，全局唯一标识
- `config_value`: 配置值，支持多种数据类型
- `config_type`: 配置类型（string, number, boolean, json, text）
- `config_group`: 配置分组，便于管理和查询
- `is_encrypted`: 是否加密存储敏感配置
- `is_public`: 是否允许公开访问
- `version`: 配置版本号，支持变更追踪

#### 配置分组设计
- **point**: 积分系统相关配置
- **app**: APP管理相关配置
- **audit**: 审核系统相关配置
- **file**: 文件系统相关配置
- **system**: 系统基础配置
- **notification**: 通知系统配置

#### 特殊设计
- **类型安全**：通过config_type字段确保配置值的类型正确性
- **权限控制**：通过is_public字段控制配置的访问权限
- **加密存储**：敏感配置支持加密存储
- **版本管理**：自动维护配置版本号，支持变更历史追踪

### 2. 操作日志表 (ct_operation_logs)

#### 设计目标
- 记录系统中所有重要操作的详细信息
- 支持多种操作人类型和操作场景
- 提供完整的审计追踪能力
- 支持性能监控和问题诊断

#### 核心字段
- `operator_id/operator_type`: 操作人信息和类型
- `module/action`: 操作模块和具体动作
- `resource_type/resource_id`: 操作的资源类型和ID
- `request_params/response_data`: 请求和响应数据（JSON格式）
- `operation_result`: 操作结果（成功/失败/部分成功）
- `processing_time`: 操作处理耗时
- `trace_id`: 链路追踪ID，支持分布式追踪

#### 操作人类型设计
- **user**: 普通用户操作
- **admin**: 管理员操作
- **system**: 系统自动操作
- **api**: API调用操作

#### 特殊设计
- **结构化数据**：使用JSON字段存储复杂的请求和响应数据
- **性能监控**：记录操作耗时，支持性能分析
- **链路追踪**：支持分布式系统的请求链路追踪
- **多维度索引**：支持按操作人、模块、时间等多维度查询

### 3. 统计数据表 (ct_statistics)

#### 设计目标
- 存储系统的各种统计数据和指标
- 支持多时间维度的数据聚合
- 提供灵活的统计类型和计算方式
- 支持实时和批量统计数据更新

#### 核心字段
- `stat_key/stat_category`: 统计键名和分类
- `stat_type`: 统计类型（counter, gauge, histogram, summary）
- `stat_value/stat_count/stat_sum`: 基础统计值
- `stat_avg/stat_min/stat_max`: 聚合统计值
- `time_period`: 时间周期（实时、小时、日、周、月、年）
- `stat_date/stat_hour/stat_week/stat_month/stat_year`: 时间维度字段

#### 统计类型设计
- **counter**: 计数器类型，只增不减
- **gauge**: 仪表类型，可增可减
- **histogram**: 直方图类型，分布统计
- **summary**: 摘要类型，百分位统计

#### 时间维度设计
- **realtime**: 实时统计
- **hourly**: 小时级统计
- **daily**: 日级统计
- **weekly**: 周级统计
- **monthly**: 月级统计
- **yearly**: 年级统计

#### 特殊设计
- **多维度聚合**：支持按不同时间维度的数据聚合
- **自动计算**：触发器自动计算平均值等衍生指标
- **扩展数据**：JSON字段支持复杂统计数据的存储
- **唯一约束**：防止重复统计数据的插入

## 索引设计策略

### 主要索引
1. **配置查询索引**：`uk_config_key`, `idx_config_group`, `idx_is_active`
2. **日志查询索引**：`idx_operator_id`, `idx_module`, `idx_created_at`
3. **统计查询索引**：`idx_stat_key`, `idx_stat_category`, `idx_stat_date`

### 复合索引
- `idx_group_active`: 支持按分组查询活跃配置
- `idx_module_action`: 支持按模块和动作查询日志
- `idx_key_period_date`: 支持统计数据的时间序列查询

## 约束设计

### 外键约束
- 配置表与管理员用户的关联约束
- 操作日志与用户的关联约束

### 唯一性约束
- 配置键名的全局唯一性
- 统计数据的多维度唯一性

### 检查约束
- 配置类型的枚举值检查
- 统计类型和时间周期的枚举值检查
- 数值字段的合理性检查

## 触发器设计

### 配置变更触发器
- 自动记录配置修改的操作日志
- 自动更新配置版本号
- 支持配置变更的审计追踪

### 统计计算触发器
- 自动设置时间维度字段
- 自动计算统计平均值
- 标记统计数据的计算状态

## 存储过程设计

### 配置管理存储过程
- `sp_get_config_value`: 获取配置值
- `sp_update_config_value`: 更新配置值
- 支持配置的安全访问和修改

### 日志记录存储过程
- `sp_log_operation`: 记录操作日志
- 标准化的日志记录接口
- 支持批量日志记录

### 统计更新存储过程
- `sp_update_statistics`: 更新统计数据
- 支持增量统计和聚合计算
- 自动处理统计数据的创建和更新

## 性能优化策略

### 查询优化
1. **配置缓存**：热点配置数据的内存缓存
2. **日志分页**：大量日志数据的分页查询优化
3. **统计预聚合**：定期预计算统计数据

### 存储优化
1. **日志分区**：按时间对日志表进行分区
2. **统计归档**：历史统计数据的归档策略
3. **JSON压缩**：复杂数据的压缩存储

### 并发优化
1. **配置读写分离**：配置读取的缓存优化
2. **日志异步写入**：操作日志的异步批量写入
3. **统计批量更新**：统计数据的批量更新机制

## 安全设计

### 配置安全
- 敏感配置的加密存储
- 配置访问权限控制
- 配置修改的审批流程

### 日志安全
- 敏感数据的脱敏处理
- 日志数据的完整性保护
- 日志访问权限控制

### 统计安全
- 统计数据的访问权限控制
- 敏感统计指标的保护
- 统计数据的匿名化处理

## 业务规则实现

### 配置管理规则
1. **配置验证**：配置值的格式和范围验证
2. **配置依赖**：相关配置的依赖关系检查
3. **配置生效**：配置修改的生效机制
4. **配置回滚**：配置错误的快速回滚

### 日志记录规则
1. **日志级别**：不同操作的日志记录级别
2. **敏感过滤**：敏感信息的自动过滤
3. **日志聚合**：相似操作的日志聚合
4. **日志清理**：历史日志的自动清理

### 统计计算规则
1. **统计周期**：不同指标的统计周期设置
2. **数据聚合**：多维度数据的聚合规则
3. **异常检测**：统计数据的异常值检测
4. **数据修复**：统计数据的自动修复机制

## 扩展性设计

### 功能扩展
- 配置模板和批量配置
- 实时日志流处理
- 机器学习统计分析
- 自定义统计指标

### 性能扩展
- 分布式配置管理
- 日志数据的流式处理
- 统计数据的实时计算
- 多级缓存架构

### 集成扩展
- 配置中心集成
- 日志分析系统集成
- 监控告警系统集成
- 数据可视化平台集成

## 监控和运维

### 监控指标
- 配置修改频率和影响范围
- 操作日志的增长速度和存储占用
- 统计计算的准确性和及时性
- 系统性能和资源使用情况

### 运维策略
- 配置备份和恢复机制
- 日志数据的归档和清理
- 统计数据的校验和修复
- 系统性能监控和优化

### 质量保证
- 配置变更的测试验证
- 日志数据的完整性检查
- 统计结果的准确性验证
- 系统稳定性和可靠性保证