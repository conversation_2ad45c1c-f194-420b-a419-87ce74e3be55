-- =============================================
-- 系统配置和统计表结构设计
-- 创建时间: 2025-01-08
-- 描述: 支持次推应用的系统配置管理、操作日志记录和数据统计分析功能
-- =============================================

-- 1. 系统配置表 (ct_system_configs)
-- 用途: 管理系统的各种配置参数，支持动态配置和版本管理
CREATE TABLE ct_system_configs (
    config_id INT PRIMARY KEY AUTO_INCREMENT COMMENT '配置ID',
    config_key VARCHAR(100) NOT NULL COMMENT '配置键名',
    config_name VARCHAR(200) NOT NULL COMMENT '配置名称',
    config_value TEXT COMMENT '配置值',
    config_type ENUM('string', 'number', 'boolean', 'json', 'text') DEFAULT 'string' COMMENT '配置类型',
    config_group VARCHAR(50) DEFAULT 'system' COMMENT '配置分组',
    config_description TEXT COMMENT '配置描述',
    default_value TEXT COMMENT '默认值',
    validation_rule VARCHAR(500) COMMENT '验证规则',
    is_encrypted TINYINT(1) DEFAULT 0 COMMENT '是否加密存储(0:否 1:是)',
    is_public TINYINT(1) DEFAULT 0 COMMENT '是否公开访问(0:否 1:是)',
    is_editable TINYINT(1) DEFAULT 1 COMMENT '是否可编辑(0:否 1:是)',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否启用(0:禁用 1:启用)',
    sort_order INT DEFAULT 0 COMMENT '排序顺序',
    version INT DEFAULT 1 COMMENT '配置版本号',
    created_by BIGINT DEFAULT NULL COMMENT '创建人ID',
    updated_by BIGINT DEFAULT NULL COMMENT '最后更新人ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引设计
    UNIQUE KEY uk_config_key (config_key),
    INDEX idx_config_group (config_group),
    INDEX idx_config_type (config_type),
    INDEX idx_is_active (is_active),
    INDEX idx_is_public (is_public),
    INDEX idx_sort_order (sort_order),
    INDEX idx_created_at (created_at),
    INDEX idx_updated_at (updated_at),
    
    -- 复合索引
    INDEX idx_group_active (config_group, is_active),
    INDEX idx_public_active (is_public, is_active),
    
    -- 外键约束
    FOREIGN KEY (created_by) REFERENCES ct_admin_users(admin_id) ON DELETE SET NULL,
    FOREIGN KEY (updated_by) REFERENCES ct_admin_users(admin_id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';

-- 2. 操作日志表 (ct_operation_logs)
-- 用途: 记录系统中所有重要操作的详细日志，用于审计和问题追踪
CREATE TABLE ct_operation_logs (
    log_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '日志ID',
    operator_id BIGINT DEFAULT NULL COMMENT '操作人ID',
    operator_type ENUM('user', 'admin', 'system', 'api') DEFAULT 'user' COMMENT '操作人类型',
    operator_name VARCHAR(100) DEFAULT NULL COMMENT '操作人名称',
    module VARCHAR(50) NOT NULL COMMENT '操作模块',
    action VARCHAR(100) NOT NULL COMMENT '操作动作',
    resource_type VARCHAR(50) DEFAULT NULL COMMENT '资源类型',
    resource_id BIGINT DEFAULT NULL COMMENT '资源ID',
    operation_desc TEXT COMMENT '操作描述',
    request_method VARCHAR(10) DEFAULT NULL COMMENT '请求方法(GET,POST,PUT,DELETE)',
    request_url VARCHAR(500) DEFAULT NULL COMMENT '请求URL',
    request_params JSON COMMENT '请求参数',
    response_data JSON COMMENT '响应数据',
    operation_result ENUM('success', 'failure', 'partial') DEFAULT 'success' COMMENT '操作结果',
    error_message TEXT COMMENT '错误信息',
    ip_address VARCHAR(45) DEFAULT NULL COMMENT '操作IP地址',
    user_agent VARCHAR(500) DEFAULT NULL COMMENT '用户代理',
    session_id VARCHAR(100) DEFAULT NULL COMMENT '会话ID',
    trace_id VARCHAR(100) DEFAULT NULL COMMENT '链路追踪ID',
    processing_time INT DEFAULT NULL COMMENT '处理耗时(毫秒)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    -- 索引设计
    INDEX idx_operator_id (operator_id),
    INDEX idx_operator_type (operator_type),
    INDEX idx_module (module),
    INDEX idx_action (action),
    INDEX idx_resource_type (resource_type),
    INDEX idx_resource_id (resource_id),
    INDEX idx_operation_result (operation_result),
    INDEX idx_ip_address (ip_address),
    INDEX idx_created_at (created_at),
    INDEX idx_session_id (session_id),
    INDEX idx_trace_id (trace_id),
    
    -- 复合索引
    INDEX idx_operator_module (operator_id, module),
    INDEX idx_module_action (module, action),
    INDEX idx_resource_type_id (resource_type, resource_id),
    INDEX idx_result_time (operation_result, created_at),
    INDEX idx_operator_time (operator_id, created_at),
    
    -- 外键约束
    FOREIGN KEY (operator_id) REFERENCES ct_users(user_id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='操作日志表';

-- 3. 统计数据表 (ct_statistics)
-- 用途: 存储系统的各种统计数据，支持多维度的数据分析和报表生成
CREATE TABLE ct_statistics (
    stat_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '统计ID',
    stat_key VARCHAR(100) NOT NULL COMMENT '统计键名',
    stat_name VARCHAR(200) NOT NULL COMMENT '统计名称',
    stat_type ENUM('counter', 'gauge', 'histogram', 'summary') DEFAULT 'counter' COMMENT '统计类型',
    stat_category VARCHAR(50) NOT NULL COMMENT '统计分类',
    stat_dimension VARCHAR(100) DEFAULT NULL COMMENT '统计维度',
    stat_value DECIMAL(20,4) NOT NULL DEFAULT 0 COMMENT '统计值',
    stat_count BIGINT DEFAULT 0 COMMENT '统计次数',
    stat_sum DECIMAL(20,4) DEFAULT 0 COMMENT '统计总和',
    stat_avg DECIMAL(20,4) DEFAULT 0 COMMENT '统计平均值',
    stat_min DECIMAL(20,4) DEFAULT 0 COMMENT '统计最小值',
    stat_max DECIMAL(20,4) DEFAULT 0 COMMENT '统计最大值',
    stat_data JSON COMMENT '扩展统计数据',
    time_period ENUM('realtime', 'hourly', 'daily', 'weekly', 'monthly', 'yearly') DEFAULT 'daily' COMMENT '时间周期',
    stat_date DATE NOT NULL COMMENT '统计日期',
    stat_hour TINYINT DEFAULT NULL COMMENT '统计小时(0-23)',
    stat_week TINYINT DEFAULT NULL COMMENT '统计周(1-53)',
    stat_month TINYINT DEFAULT NULL COMMENT '统计月(1-12)',
    stat_year SMALLINT DEFAULT NULL COMMENT '统计年',
    is_calculated TINYINT(1) DEFAULT 0 COMMENT '是否已计算(0:否 1:是)',
    calculation_time TIMESTAMP NULL DEFAULT NULL COMMENT '计算时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引设计
    INDEX idx_stat_key (stat_key),
    INDEX idx_stat_category (stat_category),
    INDEX idx_stat_type (stat_type),
    INDEX idx_time_period (time_period),
    INDEX idx_stat_date (stat_date),
    INDEX idx_stat_hour (stat_hour),
    INDEX idx_stat_week (stat_week),
    INDEX idx_stat_month (stat_month),
    INDEX idx_stat_year (stat_year),
    INDEX idx_is_calculated (is_calculated),
    INDEX idx_created_at (created_at),
    
    -- 复合索引
    INDEX idx_key_date (stat_key, stat_date),
    INDEX idx_category_date (stat_category, stat_date),
    INDEX idx_key_period_date (stat_key, time_period, stat_date),
    INDEX idx_category_period_date (stat_category, time_period, stat_date),
    INDEX idx_date_period (stat_date, time_period),
    
    -- 唯一约束
    UNIQUE KEY uk_stat_unique (stat_key, stat_category, stat_dimension, time_period, stat_date, stat_hour)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='统计数据表';

-- =============================================
-- 初始化数据
-- =============================================

-- 插入系统配置基础数据
INSERT INTO ct_system_configs (config_key, config_name, config_value, config_type, config_group, config_description, default_value, is_public, sort_order) VALUES
-- 积分系统配置
('point.evaluation.reward', '评测报告奖励积分', '10', 'number', 'point', '用户提交评测报告获得的积分奖励', '10', 0, 1),
('point.clue.reward', '线索提交奖励积分', '5', 'number', 'point', '用户提交放水线索获得的积分奖励', '5', 0, 2),
('point.daily.signin', '每日签到积分', '2', 'number', 'point', '用户每日签到获得的积分', '2', 0, 3),
('point.invite.reward', '邀请奖励积分', '20', 'number', 'point', '成功邀请新用户获得的积分奖励', '20', 0, 4),

-- APP分类配置
('app.categories', 'APP分类配置', '["社交通讯","金融理财","生活服务","购物消费","游戏娱乐","工具效率","其他"]', 'json', 'app', 'APP应用的分类列表', '[]', 1, 10),
('app.logo.max_size', 'APP图标最大尺寸', '2097152', 'number', 'app', 'APP图标文件的最大字节数(2MB)', '2097152', 0, 11),
('app.logo.allowed_formats', 'APP图标允许格式', 'jpg,jpeg,png,webp', 'string', 'app', 'APP图标允许的文件格式', 'jpg,jpeg,png,webp', 0, 12),

-- 审核系统配置
('audit.auto_enabled', '启用自动审核', 'true', 'boolean', 'audit', '是否启用自动审核功能', 'true', 0, 20),
('audit.timeout_hours', '默认审核超时时间', '48', 'number', 'audit', '审核任务的默认超时时间(小时)', '48', 0, 21),
('audit.keywords.sensitive', '敏感词库', '["违法","色情","暴力","赌博","毒品"]', 'json', 'audit', '自动审核的敏感词库', '[]', 0, 22),
('audit.pass_threshold', '自动通过阈值', '80', 'number', 'audit', '自动审核通过的分数阈值', '80', 0, 23),
('audit.reject_threshold', '自动拒绝阈值', '20', 'number', 'audit', '自动审核拒绝的分数阈值', '20', 0, 24),

-- 文件系统配置
('file.upload.max_size', '文件上传最大尺寸', '10485760', 'number', 'file', '单个文件上传的最大字节数(10MB)', '10485760', 0, 30),
('file.storage.type', '文件存储类型', 'local', 'string', 'file', '文件存储方式(local,oss,cos,qiniu)', 'local', 0, 31),
('file.cdn.domain', 'CDN域名', '', 'string', 'file', '文件访问的CDN域名', '', 0, 32),

-- 系统基础配置
('system.name', '系统名称', '次推', 'string', 'system', '系统的显示名称', '次推', 1, 100),
('system.version', '系统版本', '1.0.0', 'string', 'system', '当前系统版本号', '1.0.0', 1, 101),
('system.maintenance', '维护模式', 'false', 'boolean', 'system', '是否开启系统维护模式', 'false', 0, 102),
('system.register_enabled', '开放注册', 'true', 'boolean', 'system', '是否允许用户注册', 'true', 1, 103),

-- 通知配置
('notification.sms.enabled', '短信通知启用', 'true', 'boolean', 'notification', '是否启用短信通知功能', 'true', 0, 110),
('notification.email.enabled', '邮件通知启用', 'false', 'boolean', 'notification', '是否启用邮件通知功能', 'false', 0, 111);

-- =============================================
-- 触发器设计
-- =============================================

-- 配置变更日志触发器：记录配置修改历史
DELIMITER $$
CREATE TRIGGER tr_config_change_log
AFTER UPDATE ON ct_system_configs
FOR EACH ROW
BEGIN
    -- 如果配置值发生变化，记录操作日志
    IF OLD.config_value != NEW.config_value THEN
        INSERT INTO ct_operation_logs (
            operator_id,
            operator_type,
            module,
            action,
            resource_type,
            resource_id,
            operation_desc,
            request_params,
            operation_result
        ) VALUES (
            NEW.updated_by,
            'admin',
            'system_config',
            'update_config',
            'config',
            NEW.config_id,
            CONCAT('配置项 ', NEW.config_name, ' 值变更'),
            JSON_OBJECT(
                'config_key', NEW.config_key,
                'old_value', OLD.config_value,
                'new_value', NEW.config_value,
                'version', NEW.version
            ),
            'success'
        );
    END IF;
    
    -- 自动更新版本号
    IF OLD.config_value != NEW.config_value THEN
        SET NEW.version = OLD.version + 1;
    END IF;
END$$
DELIMITER ;

-- 统计数据计算触发器：自动计算统计指标
DELIMITER $$
CREATE TRIGGER tr_statistics_calculate
BEFORE INSERT ON ct_statistics
FOR EACH ROW
BEGIN
    -- 自动设置时间维度字段
    SET NEW.stat_hour = CASE WHEN NEW.time_period IN ('realtime', 'hourly') THEN HOUR(NEW.stat_date) ELSE NULL END;
    SET NEW.stat_week = CASE WHEN NEW.time_period = 'weekly' THEN WEEK(NEW.stat_date) ELSE NULL END;
    SET NEW.stat_month = CASE WHEN NEW.time_period IN ('monthly', 'yearly') THEN MONTH(NEW.stat_date) ELSE NULL END;
    SET NEW.stat_year = YEAR(NEW.stat_date);
    
    -- 自动计算平均值
    IF NEW.stat_count > 0 THEN
        SET NEW.stat_avg = NEW.stat_sum / NEW.stat_count;
    END IF;
END$$
DELIMITER ;

DELIMITER $$
CREATE TRIGGER tr_statistics_update_calculate
BEFORE UPDATE ON ct_statistics
FOR EACH ROW
BEGIN
    -- 自动计算平均值
    IF NEW.stat_count > 0 THEN
        SET NEW.stat_avg = NEW.stat_sum / NEW.stat_count;
    END IF;
    
    -- 标记为已计算
    IF OLD.stat_value != NEW.stat_value OR OLD.stat_count != NEW.stat_count THEN
        SET NEW.is_calculated = 1;
        SET NEW.calculation_time = CURRENT_TIMESTAMP;
    END IF;
END$$
DELIMITER ;

-- =============================================
-- 存储过程设计
-- =============================================

-- 获取配置值的存储过程
DELIMITER $$
CREATE PROCEDURE sp_get_config_value(
    IN p_config_key VARCHAR(100),
    OUT p_config_value TEXT
)
BEGIN
    DECLARE v_value TEXT DEFAULT NULL;
    
    SELECT config_value INTO v_value
    FROM ct_system_configs
    WHERE config_key = p_config_key
    AND is_active = 1;
    
    SET p_config_value = v_value;
END$$
DELIMITER ;

-- 更新配置值的存储过程
DELIMITER $$
CREATE PROCEDURE sp_update_config_value(
    IN p_config_key VARCHAR(100),
    IN p_config_value TEXT,
    IN p_updated_by BIGINT
)
BEGIN
    DECLARE v_exists INT DEFAULT 0;
    
    SELECT COUNT(*) INTO v_exists
    FROM ct_system_configs
    WHERE config_key = p_config_key;
    
    IF v_exists > 0 THEN
        UPDATE ct_system_configs
        SET config_value = p_config_value,
            updated_by = p_updated_by,
            updated_at = CURRENT_TIMESTAMP
        WHERE config_key = p_config_key;
    ELSE
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = '配置项不存在';
    END IF;
END$$
DELIMITER ;

-- 记录操作日志的存储过程
DELIMITER $$
CREATE PROCEDURE sp_log_operation(
    IN p_operator_id BIGINT,
    IN p_operator_type VARCHAR(20),
    IN p_module VARCHAR(50),
    IN p_action VARCHAR(100),
    IN p_resource_type VARCHAR(50),
    IN p_resource_id BIGINT,
    IN p_operation_desc TEXT,
    IN p_ip_address VARCHAR(45),
    IN p_processing_time INT
)
BEGIN
    INSERT INTO ct_operation_logs (
        operator_id,
        operator_type,
        module,
        action,
        resource_type,
        resource_id,
        operation_desc,
        ip_address,
        processing_time,
        operation_result
    ) VALUES (
        p_operator_id,
        p_operator_type,
        p_module,
        p_action,
        p_resource_type,
        p_resource_id,
        p_operation_desc,
        p_ip_address,
        p_processing_time,
        'success'
    );
END$$
DELIMITER ;

-- 更新统计数据的存储过程
DELIMITER $$
CREATE PROCEDURE sp_update_statistics(
    IN p_stat_key VARCHAR(100),
    IN p_stat_category VARCHAR(50),
    IN p_stat_dimension VARCHAR(100),
    IN p_time_period VARCHAR(20),
    IN p_stat_date DATE,
    IN p_increment_value DECIMAL(20,4)
)
BEGIN
    DECLARE v_exists INT DEFAULT 0;
    
    SELECT COUNT(*) INTO v_exists
    FROM ct_statistics
    WHERE stat_key = p_stat_key
    AND stat_category = p_stat_category
    AND IFNULL(stat_dimension, '') = IFNULL(p_stat_dimension, '')
    AND time_period = p_time_period
    AND stat_date = p_stat_date;
    
    IF v_exists > 0 THEN
        UPDATE ct_statistics
        SET stat_value = stat_value + p_increment_value,
            stat_count = stat_count + 1,
            stat_sum = stat_sum + p_increment_value,
            stat_max = GREATEST(stat_max, p_increment_value),
            stat_min = LEAST(stat_min, p_increment_value),
            updated_at = CURRENT_TIMESTAMP
        WHERE stat_key = p_stat_key
        AND stat_category = p_stat_category
        AND IFNULL(stat_dimension, '') = IFNULL(p_stat_dimension, '')
        AND time_period = p_time_period
        AND stat_date = p_stat_date;
    ELSE
        INSERT INTO ct_statistics (
            stat_key,
            stat_name,
            stat_category,
            stat_dimension,
            time_period,
            stat_date,
            stat_value,
            stat_count,
            stat_sum,
            stat_min,
            stat_max
        ) VALUES (
            p_stat_key,
            p_stat_key,
            p_stat_category,
            p_stat_dimension,
            p_time_period,
            p_stat_date,
            p_increment_value,
            1,
            p_increment_value,
            p_increment_value,
            p_increment_value
        );
    END IF;
END$$
DELIMITER ;