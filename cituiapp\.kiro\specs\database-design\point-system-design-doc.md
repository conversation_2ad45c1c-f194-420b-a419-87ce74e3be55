# 积分和奖励系统表结构设计文档

## 概述

积分和奖励系统是次推应用的核心功能模块，用于激励用户参与平台活动。系统支持积分获取、消耗、兑换等完整流程，并提供灵活的规则配置机制。

## 表结构设计

### 1. 积分记录表 (ct_point_records)

**业务用途**：记录用户所有积分变动的详细历史，确保积分流水的完整性和可追溯性。

**核心字段说明**：
- `point_change`: 积分变动数量，正数表示增加，负数表示扣除
- `point_balance`: 变动后的积分余额，便于快速查询历史余额
- `change_type`: 变动类型，支持评测提交、线索提交、审核通过/拒绝、手动调整等
- `related_type/related_id`: 关联业务对象，建立积分变动与具体业务的关联

**设计考虑**：
- 使用复合索引 `(related_type, related_id)` 支持按业务对象查询积分记录
- 记录操作人ID，区分系统自动操作和管理员手动操作
- 支持软删除机制，通过status字段控制记录有效性

### 2. 积分规则表 (ct_point_rules)

**业务用途**：配置不同行为的积分奖励规则，支持灵活的积分策略管理。

**核心字段说明**：
- `rule_code`: 规则唯一标识，用于程序中引用特定规则
- `point_value`: 积分数值，支持正负数表示奖励和扣除
- `daily_limit/total_limit`: 限制机制，防止积分滥用
- `valid_start_date/valid_end_date`: 规则有效期，支持限时活动

**设计考虑**：
- 使用优先级字段支持规则冲突时的处理逻辑
- 支持条件描述字段，便于复杂规则的说明
- 规则代码使用唯一约束，确保规则标识的唯一性

### 3. 奖励配置表 (ct_reward_configs)

**业务用途**：管理积分兑换商品和奖励配置，支持多种奖励类型。

**核心字段说明**：
- `config_type`: 配置类型，支持积分兑换、等级奖励、活动奖励等
- `reward_type`: 奖励类型，支持积分、虚拟物品、实物等
- `reward_value`: 奖励内容，使用JSON格式存储复杂奖励信息
- `stock_quantity`: 库存管理，支持限量商品的库存控制

**设计考虑**：
- 支持时间范围控制，便于限时活动管理
- 使用排序字段控制商品展示顺序
- 支持多维度限制：兑换次数、每日限制、库存限制

### 4. 用户积分汇总表 (ct_user_points)

**业务用途**：快速查询用户当前积分状态，避免实时计算带来的性能问题。

**核心字段说明**：
- `available_points`: 可用积分，扣除冻结积分后的可使用积分
- `frozen_points`: 冻结积分，用于处理退款、争议等场景
- `level`: 用户等级，基于积分计算的用户等级
- `level_progress`: 当前等级进度，距离下一等级的积分差距

**设计考虑**：
- 使用用户ID作为主键，确保每个用户只有一条记录
- 记录累计获得和消耗积分，便于统计分析
- 支持等级系统，增强用户粘性

### 5. 积分兑换记录表 (ct_point_exchanges)

**业务用途**：记录用户积分兑换历史，支持兑换状态管理和处理流程。

**核心字段说明**：
- `exchange_status`: 兑换状态，支持待处理、已发放、已取消、发放失败等状态
- `reward_content`: 奖励内容描述，记录具体兑换的商品信息
- `process_time/process_by`: 处理时间和处理人，支持人工审核流程

**设计考虑**：
- 关联奖励配置表，保持兑换记录与配置的一致性
- 支持状态流转，便于兑换流程管理
- 记录处理人信息，支持责任追溯

## 业务流程设计

### 积分获取流程
1. 用户执行特定行为（提交评测、提交线索等）
2. 系统根据积分规则表查找对应规则
3. 检查规则限制条件（每日限制、总限制等）
4. 在积分记录表中创建积分变动记录
5. 更新用户积分汇总表中的积分余额

### 积分兑换流程
1. 用户选择兑换商品，检查积分余额和兑换条件
2. 创建积分兑换记录，状态为待处理
3. 扣除用户可用积分，增加冻结积分
4. 管理员处理兑换请求，更新兑换状态
5. 根据处理结果调整用户积分（成功则扣除冻结积分，失败则恢复可用积分）

### 审核积分发放流程
1. 用户提交内容，系统预发放积分（状态为待审核）
2. 管理员审核内容
3. 审核通过：确认积分发放，更新积分记录状态
4. 审核拒绝：撤销积分发放，创建扣除记录

## 性能优化策略

### 索引设计
- **积分记录表**：用户ID索引、变动类型索引、关联业务复合索引
- **积分规则表**：规则代码唯一索引、状态索引、有效期复合索引
- **奖励配置表**：配置代码唯一索引、配置类型索引、时间范围复合索引

### 查询优化
- 使用用户积分汇总表避免实时计算积分余额
- 通过状态字段快速过滤有效记录
- 使用时间范围索引优化历史记录查询

### 数据一致性
- 使用事务确保积分变动的原子性
- 定期校验积分汇总表与明细记录的一致性
- 使用乐观锁防止并发操作导致的数据不一致

## 扩展性考虑

### 规则引擎扩展
- 支持复杂条件表达式的规则配置
- 支持规则组合和优先级处理
- 支持动态规则加载和热更新

### 奖励类型扩展
- 支持优惠券、会员权益等虚拟奖励
- 支持第三方商品接入
- 支持个性化奖励推荐

### 统计分析扩展
- 支持积分流水的多维度统计
- 支持用户行为分析和积分预测
- 支持A/B测试的积分策略对比