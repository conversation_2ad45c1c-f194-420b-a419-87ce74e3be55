# CitUI Laravel 后端 API 开发实现任务

## 任务概述

基于需求分析和系统设计，将 CitUI Laravel 后端 API 开发分解为一系列可执行的编码任务。每个任务都是独立的、可测试的，并且按照依赖关系进行排序，确保增量式开发和早期验证。

## 实现任务列表

- [x] 1. 项目基础架构搭建

  - 创建 CitUI 模块的目录结构
  - 配置独立的路由文件
  - 建立基础的控制器、服务和模型类
  - _需求: 1.1, 1.2, 1.3, 1.4_

- [ ] 2. 数据库 SQL 文件创建

  - [x] 2.1 创建用户相关表的 SQL 文件

    - 创建 ct_users、ct_admin_users、ct_user_logins、ct_user_relations 表的 MySQL 5.7 兼容 SQL 文件
    - 确保字段定义与原始 SQL 文件一致，优化索引结构
    - 生成可直接在 MySQL 中执行的建表语句
    - _需求: 2.1, 2.2_

  - [x] 2.2 创建 APP 相关表的 SQL 文件

    - 创建 ct_app_categories、ct_apps 表的 MySQL 5.7 兼容 SQL 文件
    - 定义分类层级结构和 APP 信息字段
    - 生成可直接在 MySQL 中执行的建表语句
    - _需求: 2.1, 2.2_

  - [x] 2.3 创建评测系统表的 SQL 文件

    - 创建 ct_evaluation_reports、ct_evaluation_details 表的 MySQL 5.7 兼容 SQL 文件
    - 设计评测数据的存储结构
    - 生成可直接在 MySQL 中执行的建表语句
    - _需求: 2.1, 2.2_

  - [x] 2.4 创建线索系统表的 SQL 文件

    - 创建 ct_water_clues、ct_clue_feedbacks、ct_clue_statistics 表的 MySQL 5.7 兼容 SQL 文件
    - 设计线索反馈和统计数据结构
    - 生成可直接在 MySQL 中执行的建表语句
    - _需求: 2.1, 2.2_

  - [x] 2.5 创建积分系统表的 SQL 文件

    - 创建 ct_point_rules、ct_reward_configs、ct_point_records 表的 MySQL 5.7 兼容 SQL 文件
    - 设计积分规则和奖励配置结构
    - 生成可直接在 MySQL 中执行的建表语句
    - _需求: 2.1, 2.2_

  - [x] 2.6 创建文件管理表的 SQL 文件

    - 创建 ct_file_categories、ct_files、ct_file_relations 表的 MySQL 5.7 兼容 SQL 文件
    - 设计多态文件关联结构
    - 生成可直接在 MySQL 中执行的建表语句
    - _需求: 2.1, 2.2_

  - [x] 2.7 创建审核系统表的 SQL 文件

    - 创建 ct_audit_rules、ct_content_audits、ct_audit_logs 表的 MySQL 5.7 兼容 SQL 文件
    - 设计内容审核流程数据结构
    - 生成可直接在 MySQL 中执行的建表语句
    - _需求: 2.1, 2.2_

  - [x] 2.8 创建系统配置表的 SQL 文件

    - 创建 ct_system_configs、ct_operation_logs、ct_statistics 表的 MySQL 5.7 兼容 SQL 文件
    - 设计系统配置和日志记录结构
    - 生成可直接在 MySQL 中执行的建表语句
    - _需求: 2.1, 2.2_

- [ ] 3. 基础模型类实现

  - [x] 3.1 创建 BaseCituiModel 基础模型

    - 实现统一的模型基类，包含通用字段和方法
    - 定义统一的时间戳格式和查询作用域
    - _需求: 1.3, 1.4_

  - [x] 3.2 实现用户相关模型

    - 创建 User、AdminUser、UserLogin、UserRelation 模型
    - 定义模型关联关系和属性访问器
    - 编写模型单元测试

    - _需求: 3.1, 3.2, 3.3, 3.4_

  - [x] 3.3 实现 APP 相关模型

    - 创建 AppCategory、App 模型

    - 定义分类层级关系和 APP 信息访问器
    - 编写模型单元测试
    - _需求: 4.1, 4.2, 4.3, 4.4_

  - [x] 3.4 实现评测系统模型

    - 创建 EvaluationReport、EvaluationDetail、EvaluationLike、EvaluationComment 模型
    - 定义评测报告和详细数据的关联关系
    - 实现点赞和评论功能模型
    - _需求: 5.1, 5.2, 5.3, 5.4, 5.5_

  - [x] 3.5 实现线索系统模型

    - 创建 WaterClue、ClueFeedback、ClueStatistic、ClueLike 模型
    - 定义线索、反馈和统计的关联关系
    - 实现线索点赞和统计功能
    - _需求: 6.1, 6.2, 6.3, 6.4, 6.5_

  - [x] 3.6 实现积分系统模型

    - 创建 PointRule、RewardConfig、PointRecord 模型
    - 定义积分规则和记录的关联关系
    - 实现积分计算和奖励兑换逻辑
    - _需求: 7.1, 7.2, 7.3, 7.4_

  - [x] 3.7 实现文件管理模型

    - 创建 FileCategory、File、FileRelation 模型
    - 定义多态文件关联关系
    - 编写模型单元测试
    - _需求: 8.1, 8.2, 8.3, 8.4_

  - [x] 3.8 实现审核系统模型

    - 创建 AuditRule、ContentAudit、AuditLog 模型
    - 定义审核流程的关联关系
    - 编写模型单元测试
    - _需求: 9.1, 9.2, 9.3, 9.4_

- [ ] 4. 核心服务层实现

  - [x] 4.1 实现 BaseService 基础服务类

    - 创建统一的服务基类，继承现有 BaseService
    - 实现通用的分页、缓存和日志记录功能
    - _需求: 1.3, 1.4_

  - [x] 4.2 实现 AuthService 认证服务

    - 实现用户注册功能，包括手机号验证和积分奖励
    - 实现用户登录功能，包括 Token 生成和登录日志
    - 实现短信验证码发送和验证功能
    - _需求: 3.1, 3.2, 3.5_

  - [x] 4.3 实现 UserService 用户服务

    - 实现用户信息查询和更新功能
    - 实现头像上传和文件关联功能
    - 实现密码修改和用户关系管理功能
    - _需求: 3.2, 3.3, 3.4_

  - [x] 4.4 实现 FileService 文件服务

    - 实现文件上传功能，仅支持本地就可以
    - 实现文件关联和访问控制功能
    - 实现批量文件上传和管理功能
    - _需求: 8.1, 8.2, 8.3, 8.4_

  - [x] 4.5 实现 AppService 应用服务

    - 实现 APP 列表查询，支持分类筛选和搜索
    - 实现 APP 详情查询和统计更新功能
    - 实现推荐 APP 算法和缓存策略
    - _需求: 4.1, 4.2, 4.3, 4.4_

  - [x] 4.6 实现 PointService 积分服务

    - 实现积分规则管理和自动发放功能
    - 实现积分记录查询和统计功能
    - 实现奖励兑换和库存管理功能
    - _需求: 7.1, 7.2, 7.3, 7.4_

  - [x] 4.7 实现 AuditService 审核服务

    - 实现内容自动审核和规则匹配功能
    - 实现审核流程管理和状态更新功能
    - 实现审核日志记录和通知功能
    - _需求: 9.1, 9.2, 9.3, 9.4_

- [x] 5. 认证授权系统实现

  - [x] 5.1 实现 AuthController 认证控制器

    - 创建用户注册 API 端点，集成验证码验证
    - 创建用户登录 API 端点，返回 Token 和用户信息
    - 创建短信验证码发送 API 端点
    - 创建用户登出和 Token 刷新 API 端点
    - _需求: 3.1, 3.2, 3.5_

  - [x] 5.2 实现认证中间件

    - 创建 CituiAuthMiddleware 中间件，验证 API Token
    - 实现用户信息注入和权限检查
    - 集成 Laravel Sanctum 认证系统
    - _需求: 3.5_

  - [x] 5.3 配置路由和中间件

    - 在 citui.php 路由文件中配置认证相关路由
    - 应用认证中间件到需要保护的路由组
    - _需求: 3.5_

- [x] 6. 用户管理系统实现

  - [x] 6.1 实现 UserController 用户控制器

    - 创建用户信息查询 API 端点
    - 创建用户信息更新 API 端点
    - 创建头像上传 API 端点
    - 创建密码修改 API 端点
    - 创建用户关系管理 API 端点
    - _需求: 3.2, 3.3, 3.4_

  - [x] 6.2 实现用户数据验证

    - 创建用户信息更新的表单验证类
    - 创建头像上传的文件验证规则
    - 创建密码修改的安全验证
    - _需求: 3.2, 3.3, 3.4_

- [x] 7. APP 管理系统实现

  - [x] 7.1 实现 AppController 应用控制器

    - 创建 APP 分类列表 API 端点
    - 创建 APP 列表查询 API 端点，支持筛选和搜索
    - 创建 APP 详情查询 API 端点
    - 创建推荐 APP 查询 API 端点
    - _需求: 4.1, 4.2, 4.3, 4.4_

  - [x] 7.2 实现 APP 搜索功能

    - 集成全文搜索功能，支持 APP 名称和描述搜索
    - 实现搜索结果排序和高亮显示
    - 实现搜索历史记录和热门搜索
    - _需求: 4.1, 4.2_

  - [x] 7.3 实现 APP 统计功能

    - 实现 APP 查看次数统计更新
    - 实现 APP 下载次数统计
    - 实现 APP 评分统计计算
    - _需求: 4.4_

- [x] 8. 评测系统实现

  - [x] 8.1 实现 EvaluationController 评测控制器

    - 创建评测报告提交 API 端点
    - 创建评测报告列表查询 API 端点
    - 创建评测报告详情查询 API 端点
    - 创建评测报告点赞 API 端点
    - 创建我的评测报告查询 API 端点
    - _需求: 5.1, 5.2, 5.3, 5.4_

  - [x] 8.2 实现 EvaluationService 评测服务

    - 实现评测报告提交逻辑，包括文件处理和审核提交
    - 实现评测报告查询逻辑，支持多维度筛选
    - 实现评测报告统计和推荐算法
    - _需求: 5.1, 5.2, 5.3, 5.4, 5.5_

  - [x] 8.3 实现评测数据验证

    - 创建评测报告提交的表单验证类
    - 实现评测数据的业务逻辑验证
    - 实现重复提交检查和频率限制
    - _需求: 5.1, 5.5_

- [x] 9. 线索系统实现

  - [x] 9.1 实现 ClueController 线索控制器

    - 创建线索发布 API 端点
    - 创建线索列表查询 API 端点
    - 创建线索详情查询 API 端点
    - 创建线索反馈提交 API 端点
    - 创建我的线索查询 API 端点
    - _需求: 6.1, 6.2, 6.3, 6.4_

  - [x] 9.2 实现 ClueService 线索服务

    - 实现线索发布逻辑，包括审核和积分奖励
    - 实现线索反馈处理和统计更新
    - 实现线索成功率计算和排序算法
    - _需求: 6.1, 6.2, 6.3, 6.4, 6.5_

  - [x] 9.3 实现线索统计功能

    - 实现线索查看、尝试、成功统计的实时更新
    - 实现线索收益统计和成功率计算
    - 实现线索过期处理和状态更新
    - _需求: 6.4, 6.5_

- [x] 10. 积分系统实现


  - [x] 10.1 实现 PointController 积分控制器

    - 创建积分记录查询 API 端点
    - 创建积分规则查询 API 端点
    - 创建奖励配置查询 API 端点
    - 创建奖励兑换 API 端点
    - _需求: 7.1, 7.2, 7.3, 7.4_

  - [x] 10.2 实现积分自动发放机制

    - 实现注册奖励积分自动发放
    - 实现评测报告奖励积分自动发放
    - 实现线索发布奖励积分自动发放
    - 实现反馈奖励积分自动发放
    - _需求: 7.1, 7.4_

  - [x] 10.3 实现积分兑换功能

    - 实现奖励兑换逻辑和库存管理
    - 实现兑换记录和状态跟踪
    - 实现兑换通知和物流处理
    - _需求: 7.3, 7.4_

- [x] 11. 文件管理系统实现






  - [x] 11.1 实现 FileController 文件控制器



    - 创建文件上传 API 端点，支持单文件和批量上传
    - 创建文件信息查询 API 端点
    - 创建文件删除 API 端点
    - 创建文件分类查询 API 端点

    - _需求: 8.1, 8.2, 8.4_

  - [x] 11.2 实现文件存储策略



    - 配置本地文件存储驱动
    - _需求: 8.2_

  - [x] 11.3 实现文件安全控制




    - 实现文件类型和大小验证
    - 实现文件访问权限控制
    - 实现恶意文件检测和防护

    - _需求: 8.4_

- [-] 12. 内容审核系统实现




  - [x] 12.1 实现自动审核功能



    - 实现关键词过滤和内容检测
    - 实现图片内容识别和审核
    - 实现审核分数计算和自动判定
    - _需求: 9.1, 9.2_



  - [ ] 12.2 实现人工审核功能

    - 创建审核任务分配和管理功能
    - 实现审核界面和操作记录
    - 实现审核结果通知和状态更新



    - _需求: 9.2, 9.3_

  - [ ] 12.3 实现审核流程集成

    - 集成审核服务到评测报告提交流程
    - 集成审核服务到线索发布流程
    - 实现审核结果的积分奖励和扣除

    - _需求: 9.1, 9.2, 9.3, 9.4_

- [ ] 13. 系统配置和管理

  - [ ] 13.1 实现系统配置管理

    - 创建系统配置的 CRUD API 端点
    - 实现配置缓存和实时更新
    - 实现配置版本管理和回滚
    - _需求: 10.1, 10.2_

  - [ ] 13.2 实现操作日志记录

    - 实现关键操作的自动日志记录
    - 实现日志查询和分析功能
    - 实现日志清理和归档策略
    - _需求: 10.3_

  - [ ] 13.3 实现数据统计功能
    - 实现用户活跃度统计
    - 实现内容提交统计
    - 实现积分发放统计
    - 实现平台运营数据统计
    - _需求: 10.2, 10.4_
