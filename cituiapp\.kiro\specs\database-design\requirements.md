# 数据库表结构设计需求文档

## 介绍

本文档定义了次推（CitUI）应用的数据库表结构设计需求。次推是一个基于uni-app开发的跨平台移动应用，主要功能包括APP评测、线索分享、用户邀请和个人中心管理。

## 需求

### 需求 1：用户管理系统

**用户故事：** 作为系统管理员，我希望能够管理用户账户信息，以便提供完整的用户服务。

#### 验收标准

1. WHEN 用户注册时 THEN 系统 SHALL 创建用户基础信息记录
2. WHEN 用户登录时 THEN 系统 SHALL 记录登录信息和设备信息
3. WHEN 用户进行实名认证时 THEN 系统 SHALL 存储认证状态和信息
4. WHEN 用户建立推荐关系时 THEN 系统 SHALL 记录多级推荐关系
5. WHEN 管理员用户登录时 THEN 系统 SHALL 区分普通用户和管理员权限

### 需求 2：APP评测管理系统

**用户故事：** 作为用户，我希望能够浏览和提交APP评测报告，以便获取和分享APP使用体验。

#### 验收标准

1. WHEN 用户浏览首页时 THEN 系统 SHALL 显示APP列表和基本信息
2. WHEN 用户查看APP详情时 THEN 系统 SHALL 显示完整的评测数据
3. WHEN 用户提交评测报告时 THEN 系统 SHALL 保存所有评测信息和截图
4. WHEN 用户筛选APP时 THEN 系统 SHALL 支持按类型、模式、福利等条件筛选
5. WHEN 系统展示评测数据时 THEN 系统 SHALL 包含测试条数、收益、时长等详细信息

### 需求 3：放水线索管理系统

**用户故事：** 作为用户，我希望能够查看和提交放水线索，以便获取最新的赚钱机会信息。

#### 验收标准

1. WHEN 用户查看线索页面时 THEN 系统 SHALL 以时间线形式展示放水记录
2. WHEN 用户提交放水线索时 THEN 系统 SHALL 保存线索详情和相关截图
3. WHEN 用户查看线索详情时 THEN 系统 SHALL 显示提交人信息和设备信息
4. WHEN 系统展示线索时 THEN 系统 SHALL 包含放水金额、时间、描述等信息
5. WHEN 用户筛选线索时 THEN 系统 SHALL 支持按最新、高收益、热门等条件筛选

### 需求 4：积分奖励系统

**用户故事：** 作为用户，我希望通过提交内容获得积分奖励，以便激励我持续参与平台活动。

#### 验收标准

1. WHEN 用户提交评测报告时 THEN 系统 SHALL 给予50积分奖励
2. WHEN 用户提交放水线索时 THEN 系统 SHALL 给予30积分奖励
3. WHEN 用户积分发生变化时 THEN 系统 SHALL 记录积分变动历史
4. WHEN 用户查看积分时 THEN 系统 SHALL 显示当前积分和历史记录
5. WHEN 管理员审核内容时 THEN 系统 SHALL 根据审核结果发放或扣除积分

### 需求 5：文件管理系统

**用户故事：** 作为用户，我希望能够上传和管理各种截图文件，以便支持我的评测报告和线索提交。

#### 验收标准

1. WHEN 用户上传APP Logo时 THEN 系统 SHALL 保存文件信息和访问路径
2. WHEN 用户上传截图时 THEN 系统 SHALL 支持多种图片格式
3. WHEN 用户上传文件时 THEN 系统 SHALL 记录文件大小、类型、上传时间等信息
4. WHEN 系统展示内容时 THEN 系统 SHALL 正确关联和显示相关文件
5. WHEN 文件被删除时 THEN 系统 SHALL 保持数据完整性

### 需求 6：内容审核系统

**用户故事：** 作为管理员，我希望能够审核用户提交的内容，以便确保平台内容质量。

#### 验收标准

1. WHEN 用户提交内容时 THEN 系统 SHALL 设置初始审核状态为待审核
2. WHEN 管理员审核内容时 THEN 系统 SHALL 记录审核结果和审核人信息
3. WHEN 内容审核通过时 THEN 系统 SHALL 发放相应积分奖励
4. WHEN 内容审核不通过时 THEN 系统 SHALL 记录拒绝原因
5. WHEN 查询内容时 THEN 系统 SHALL 根据审核状态控制内容可见性

### 需求 7：系统配置管理

**用户故事：** 作为系统管理员，我希望能够配置系统参数，以便灵活管理平台运营。

#### 验收标准

1. WHEN 管理员配置积分规则时 THEN 系统 SHALL 保存积分奖励配置
2. WHEN 管理员配置APP类型时 THEN 系统 SHALL 支持动态添加和修改APP分类
3. WHEN 管理员配置审核规则时 THEN 系统 SHALL 支持自定义审核流程
4. WHEN 系统运行时 THEN 系统 SHALL 根据配置参数执行相应逻辑
5. WHEN 配置发生变化时 THEN 系统 SHALL 记录配置变更历史

### 需求 8：数据统计分析

**用户故事：** 作为管理员，我希望能够查看平台数据统计，以便了解平台运营状况。

#### 验收标准

1. WHEN 管理员查看统计时 THEN 系统 SHALL 提供用户活跃度统计
2. WHEN 管理员查看统计时 THEN 系统 SHALL 提供内容提交量统计
3. WHEN 管理员查看统计时 THEN 系统 SHALL 提供积分发放统计
4. WHEN 管理员查看统计时 THEN 系统 SHALL 提供APP评测数据统计
5. WHEN 系统生成统计时 THEN 系统 SHALL 支持按时间段筛选统计数据