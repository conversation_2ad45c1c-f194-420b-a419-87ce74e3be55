# APP和评测相关表结构关系图

## 实体关系图 (ERD)

```mermaid
erDiagram
    ct_app_categories {
        int id PK "分类ID"
        varchar name "分类名称"
        varchar description "分类描述"
        varchar icon "分类图标URL"
        int sort_order "排序权重"
        tinyint status "状态"
        datetime create_time "创建时间"
        datetime update_time "更新时间"
    }

    ct_apps {
        int id PK "APP ID"
        varchar name "APP名称"
        int category_id FK "分类ID"
        varchar logo_url "APP Logo URL"
        varchar package_name "包名"
        varchar version "版本号"
        varchar download_url "下载链接"
        varchar register_url "注册链接"
        text description "APP描述"
        decimal rating "平均评分"
        int rating_count "评分人数"
        int download_count "下载次数"
        varchar run_mode "运行模式"
        varchar newcomer_benefit "新人福利描述"
        decimal withdraw_threshold "提现门槛金额"
        decimal guarantee_amount "顶包金额"
        tinyint is_hot "是否热门"
        tinyint is_recommended "是否推荐"
        tinyint is_water_available "是否有放水"
        tinyint status "状态"
        datetime create_time "创建时间"
        datetime update_time "更新时间"
    }

    ct_evaluation_reports {
        int id PK "报告ID"
        int app_id FK "APP ID"
        int user_id FK "提交用户ID"
        varchar title "报告标题"
        decimal rating "用户评分"
        varchar app_type "APP类型"
        varchar run_mode "运行模式"
        varchar newcomer_benefit "新人福利"
        decimal withdraw_threshold "提现门槛"
        decimal guarantee_amount "顶包金额"
        int test_count "测试条数"
        decimal total_earnings "总收益"
        int test_duration "测试时长"
        varchar test_device "测试设备"
        varchar evaluator_name "测评人姓名"
        date evaluation_date "测评日期"
        text earnings_detail "收益明细JSON"
        text report_content "测评报告内容"
        tinyint audit_status "审核状态"
        int audit_user_id "审核人ID"
        datetime audit_time "审核时间"
        varchar audit_remark "审核备注"
        int points_awarded "已奖励积分"
        int view_count "查看次数"
        int like_count "点赞次数"
        tinyint status "状态"
        datetime create_time "创建时间"
        datetime update_time "更新时间"
    }

    ct_evaluation_details {
        int id PK "详情ID"
        int report_id FK "评测报告ID"
        varchar detail_type "详情类型"
        varchar title "详情标题"
        text content "详情内容"
        varchar file_url "相关文件URL"
        int sort_order "排序权重"
        json extra_data "扩展数据JSON"
        datetime create_time "创建时间"
        datetime update_time "更新时间"
    }

    ct_users {
        int id PK "用户ID"
        varchar phone "手机号"
        varchar nick_name "昵称"
        varchar avatar_url "头像URL"
        tinyint status "状态"
        datetime create_time "创建时间"
        datetime update_time "更新时间"
    }

    %% 关系定义
    ct_app_categories ||--o{ ct_apps : "分类包含多个APP"
    ct_apps ||--o{ ct_evaluation_reports : "APP有多个评测报告"
    ct_users ||--o{ ct_evaluation_reports : "用户提交多个评测报告"
    ct_evaluation_reports ||--o{ ct_evaluation_details : "评测报告包含多个详情"
```

## 关系说明

### 1. 一对多关系

#### ct_app_categories → ct_apps (1:N)
- **关系描述**: 一个分类可以包含多个APP
- **外键**: `ct_apps.category_id` → `ct_app_categories.id`
- **业务含义**: APP按分类进行组织管理
- **约束**: 删除分类前需要先处理相关APP

#### ct_apps → ct_evaluation_reports (1:N)
- **关系描述**: 一个APP可以有多个评测报告
- **外键**: `ct_evaluation_reports.app_id` → `ct_apps.id`
- **业务含义**: 用户可以对同一个APP提交多次评测
- **约束**: APP下架后，相关评测报告保留但标记状态

#### ct_users → ct_evaluation_reports (1:N)
- **关系描述**: 一个用户可以提交多个评测报告
- **外键**: `ct_evaluation_reports.user_id` → `ct_users.id`
- **业务含义**: 用户可以评测多个不同的APP
- **约束**: 用户注销后，评测报告保留但标记用户状态

#### ct_evaluation_reports → ct_evaluation_details (1:N)
- **关系描述**: 一个评测报告可以包含多个详情记录
- **外键**: `ct_evaluation_details.report_id` → `ct_evaluation_reports.id`
- **业务含义**: 评测报告包含截图、收益明细等多种详情
- **约束**: 级联删除，删除报告时自动删除相关详情

## 数据流向分析

### 1. APP管理流程
```
ct_app_categories (创建分类)
    ↓
ct_apps (添加APP到分类)
    ↓
ct_evaluation_reports (用户提交评测)
    ↓
ct_evaluation_details (添加评测详情)
```

### 2. 评测提交流程
```
用户选择APP → 填写评测信息 → 上传截图 → 提交审核
    ↓              ↓              ↓         ↓
ct_apps    ct_evaluation_reports  文件系统  审核队列
                   ↓
            ct_evaluation_details
```

### 3. 数据统计流程
```
ct_evaluation_reports (评测数据)
    ↓
统计计算 (平均评分、评测数量等)
    ↓
ct_apps (更新统计字段)
```

## 索引策略图

### 主要查询路径
```mermaid
graph TD
    A[用户查询APP列表] --> B[按分类筛选]
    A --> C[按热门度排序]
    A --> D[按评分排序]
    
    B --> E[ct_apps.category_id索引]
    C --> F[ct_apps.is_hot索引]
    D --> G[ct_apps.rating索引]
    
    H[查看APP评测] --> I[按APP查询评测]
    I --> J[ct_evaluation_reports.app_id索引]
    
    K[用户查看自己的评测] --> L[按用户查询评测]
    L --> M[ct_evaluation_reports.user_id索引]
    
    N[管理员审核评测] --> O[按审核状态查询]
    O --> P[ct_evaluation_reports.audit_status索引]
```

## 数据完整性约束

### 外键约束
1. **ct_apps.category_id** → ct_app_categories.id
   - 确保APP必须属于有效分类
   - 删除分类时检查是否有关联APP

2. **ct_evaluation_reports.app_id** → ct_apps.id
   - 确保评测报告关联有效APP
   - APP删除时评测报告保留

3. **ct_evaluation_reports.user_id** → ct_users.id
   - 确保评测报告关联有效用户
   - 用户删除时评测报告保留

4. **ct_evaluation_details.report_id** → ct_evaluation_reports.id
   - 确保详情关联有效报告
   - 级联删除，删除报告时自动删除详情

### 业务约束
1. **评分范围**: rating字段值必须在0-5之间
2. **状态有效性**: status字段值必须在预定义范围内
3. **金额非负**: 所有金额字段必须大于等于0
4. **时间逻辑**: 更新时间必须大于等于创建时间

## 性能优化建议

### 查询优化
1. **分页查询**: 使用覆盖索引避免回表
2. **统计查询**: 考虑使用物化视图或汇总表
3. **全文搜索**: 对APP名称和评测内容建立全文索引
4. **缓存策略**: 热门APP和评测数据使用Redis缓存

### 存储优化
1. **分区策略**: 按时间对评测报告表进行分区
2. **归档策略**: 定期归档历史评测数据
3. **压缩策略**: 对大文本字段启用压缩
4. **读写分离**: 评测查询使用只读副本

## 扩展性考虑

### 水平扩展
1. **分库分表**: 按用户ID或APP ID进行分片
2. **读写分离**: 主从复制支持读写分离
3. **缓存层**: 引入Redis集群支持高并发
4. **CDN加速**: 静态资源使用CDN分发

### 功能扩展
1. **多维评分**: 支持界面、收益、稳定性等多维度评分
2. **社交功能**: 支持评论、点赞、分享等互动
3. **标签系统**: 支持APP和评测的标签管理
4. **推荐算法**: 基于用户行为的个性化推荐

## 监控指标

### 性能指标
- 查询响应时间
- 索引使用率
- 慢查询统计
- 并发连接数

### 业务指标
- 评测提交量
- 审核通过率
- APP评分分布
- 用户活跃度

### 数据质量指标
- 数据完整性检查
- 关联数据一致性
- 异常数据识别
- 重复数据统计