# 数据库表结构设计文档

## 概述

本文档基于次推（CitUI）应用的功能需求，设计了完整的MySQL数据库表结构。设计遵循数据库规范化原则，确保数据完整性、一致性和高性能。

## 架构设计

### 数据库架构原则

1. **规范化设计**：遵循第三范式，减少数据冗余
2. **性能优化**：合理设计索引，优化查询性能
3. **扩展性**：预留扩展字段，支持业务发展
4. **数据完整性**：使用外键约束和检查约束
5. **安全性**：敏感数据加密存储

### 核心模块划分

1. **用户管理模块**：用户基础信息、登录记录、关系管理
2. **内容管理模块**：APP信息、评测报告、放水线索
3. **文件管理模块**：图片上传、文件存储
4. **积分系统模块**：积分记录、奖励规则
5. **审核系统模块**：内容审核、状态管理
6. **系统配置模块**：参数配置、分类管理

## 数据模型设计

### 用户管理模块

#### 用户基础信息表 (ct_users)
- **用途**：存储用户基本信息和账户状态
- **关键字段**：用户ID、手机号、昵称、头像、注册时间
- **索引设计**：主键索引、手机号唯一索引、状态索引

#### 用户登录记录表 (ct_user_logins)
- **用途**：记录用户登录历史和设备信息
- **关键字段**：用户ID、登录时间、设备信息、IP地址
- **索引设计**：用户ID索引、登录时间索引

#### 用户关系表 (ct_user_relations)
- **用途**：管理用户推荐关系和层级结构
- **关键字段**：用户ID、推荐人ID、关系层级
- **索引设计**：用户ID索引、推荐人ID索引

### 内容管理模块

#### APP信息表 (ct_apps)
- **用途**：存储APP基础信息和分类
- **关键字段**：APP ID、名称、类型、Logo、下载量
- **索引设计**：主键索引、类型索引、状态索引

#### 评测报告表 (ct_evaluation_reports)
- **用途**：存储用户提交的APP评测报告
- **关键字段**：报告ID、APP ID、用户ID、评分、测试数据
- **索引设计**：APP ID索引、用户ID索引、提交时间索引

#### 放水线索表 (ct_water_clues)
- **用途**：存储用户提交的放水线索信息
- **关键字段**：线索ID、APP ID、用户ID、放水金额、描述
- **索引设计**：APP ID索引、用户ID索引、提交时间索引

### 文件管理模块

#### 文件信息表 (ct_files)
- **用途**：统一管理所有上传文件的信息
- **关键字段**：文件ID、原始名称、存储路径、文件大小
- **索引设计**：主键索引、文件类型索引、上传时间索引

#### 文件关联表 (ct_file_relations)
- **用途**：建立文件与业务对象的关联关系
- **关键字段**：关联ID、文件ID、业务类型、业务ID
- **索引设计**：文件ID索引、业务类型和业务ID复合索引

### 积分系统模块

#### 积分记录表 (ct_point_records)
- **用途**：记录用户积分变动历史
- **关键字段**：记录ID、用户ID、积分变动、变动原因
- **索引设计**：用户ID索引、创建时间索引

#### 积分规则表 (ct_point_rules)
- **用途**：配置不同行为的积分奖励规则
- **关键字段**：规则ID、行为类型、奖励积分、规则描述
- **索引设计**：行为类型索引、状态索引

### 审核系统模块

#### 内容审核表 (ct_content_audits)
- **用途**：管理所有内容的审核状态和记录
- **关键字段**：审核ID、内容类型、内容ID、审核状态
- **索引设计**：内容类型和内容ID复合索引、审核状态索引

### 系统配置模块

#### 系统配置表 (ct_system_configs)
- **用途**：存储系统运行参数和配置信息
- **关键字段**：配置ID、配置键、配置值、配置描述
- **索引设计**：配置键唯一索引、配置分组索引

#### APP分类表 (ct_app_categories)
- **用途**：管理APP分类信息
- **关键字段**：分类ID、分类名称、排序、状态
- **索引设计**：主键索引、状态索引、排序索引

## 错误处理

### 数据完整性约束
1. **外键约束**：确保关联数据的一致性
2. **唯一性约束**：防止重复数据
3. **检查约束**：验证数据有效性
4. **非空约束**：确保必要字段不为空

### 错误处理策略
1. **级联删除**：合理设置级联规则
2. **软删除**：重要数据使用软删除机制
3. **事务处理**：确保数据操作的原子性
4. **日志记录**：记录关键操作日志

## 测试策略

### 数据库测试方案
1. **结构测试**：验证表结构和约束
2. **性能测试**：测试查询和索引性能
3. **数据完整性测试**：验证约束和触发器
4. **并发测试**：测试多用户并发操作
5. **备份恢复测试**：验证数据备份和恢复

### 测试数据准备
1. **基础数据**：用户、APP、分类等基础数据
2. **业务数据**：评测报告、线索、积分记录
3. **边界数据**：测试数据边界和异常情况
4. **性能数据**：大量数据用于性能测试

## 部署和维护

### 部署策略
1. **环境隔离**：开发、测试、生产环境分离
2. **版本控制**：数据库结构版本管理
3. **迁移脚本**：自动化数据库升级
4. **监控告警**：数据库性能和状态监控

### 维护策略
1. **定期备份**：自动化数据备份
2. **性能优化**：定期分析和优化查询
3. **容量规划**：监控存储空间使用
4. **安全更新**：及时应用安全补丁