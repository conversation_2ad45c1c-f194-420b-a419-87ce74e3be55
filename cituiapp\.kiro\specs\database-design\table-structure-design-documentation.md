# 次推应用数据库表结构设计说明文档

## 文档概述

**文档版本**: 1.0.0  
**创建时间**: 2025-01-08  
**适用系统**: 次推应用数据库系统  
**数据库版本**: MySQL 5.7+  

本文档详细说明次推应用数据库中所有23个核心表的设计思路、业务用途、关联关系、关键字段设计考虑以及索引性能优化策略。

## 表结构分层架构

### 架构设计原则
- **分层依赖**: 按照业务依赖关系分为3层，确保数据完整性
- **模块化设计**: 每个业务模块相对独立，便于维护和扩展
- **性能优化**: 合理的索引设计和查询优化
- **安全可靠**: 完整的约束体系和数据保护机制

### 分层结构
```
第一层（基础表）: 4个表
├── ct_admin_users (管理员用户表)
├── ct_users (普通用户表)  
├── ct_app_categories (APP分类表)
└── ct_file_categories (文件分类表)

第二层（依赖基础表）: 8个表
├── ct_user_logins (用户登录记录表)
├── ct_user_relations (用户关系表)
├── ct_apps (APP信息表)
├── ct_files (文件信息表)
├── ct_point_rules (积分规则表)
├── ct_reward_configs (奖励配置表)
├── ct_system_configs (系统配置表)
└── ct_audit_rules (审核规则表)

第三层（业务表）: 11个表
├── ct_evaluation_reports (评测报告表)
├── ct_evaluation_details (评测数据详情表)
├── ct_water_clues (放水线索表)
├── ct_clue_feedbacks (线索反馈表)
├── ct_clue_statistics (线索统计表)
├── ct_point_records (积分记录表)
├── ct_file_relations (文件关联表)
├── ct_content_audits (内容审核表)
├── ct_audit_logs (审核日志表)
├── ct_operation_logs (操作日志表)
└── ct_statistics (统计数据表)
```

---

## 第一层：基础表详细设计

### 1. ct_admin_users (管理员用户表)

#### 业务用途
管理员用户表是系统管理的核心表，存储所有管理员账户信息，支持系统管理、内容审核、配置管理等管理功能。

#### 关键字段设计考虑

**主键设计**
- `admin_id`: 使用BIGINT自增主键，支持大量管理员账户

**身份认证字段**
- `username`: 管理员登录用户名，设置唯一约束防止重复
- `password_hash`: 密码哈希存储，使用VARCHAR(255)支持各种哈希算法
- `email/phone`: 联系方式，设置唯一约束，支持找回密码等功能

**权限管理字段**
- `role`: 角色字段，支持不同级别的管理员
- `permissions`: JSON格式存储细粒度权限，灵活的权限控制

**安全审计字段**
- `is_active`: 账户状态控制，支持禁用管理员账户
- `last_login_at/last_login_ip`: 登录追踪，安全审计

#### 索引设计说明
- **主键索引**: admin_id自增主键，提供高效的主键查询
- **唯一索引**: username, email, phone确保账户唯一性
- **业务索引**: is_active支持快速查询活跃管理员
- **时间索引**: created_at支持按创建时间查询和排序

#### 关联关系
- **一对多关系**: 与ct_system_configs(创建人/更新人)、ct_audit_rules(创建人)等表建立关联
- **外键策略**: 使用ON DELETE SET NULL，管理员删除时相关记录设为NULL

---

### 2. ct_users (普通用户表)

#### 业务用途
普通用户表是应用的核心用户数据表，存储所有注册用户的基本信息、积分状态、等级信息等，是整个用户体系的基础。

#### 关键字段设计考虑

**主键设计**
- `user_id`: 使用BIGINT自增主键，支持海量用户

**身份标识字段**
- `phone`: 手机号作为唯一标识，符合国内应用习惯
- `nickname`: 用户昵称，支持个性化展示
- `real_name`: 实名信息，支持实名认证需求

**积分系统字段**
- `total_points`: 历史总积分，记录用户累计获得的所有积分
- `available_points`: 可用积分，当前可以使用的积分余额
- `level`: 用户等级，基于积分或活跃度的等级体系

**用户画像字段**
- `gender/birthday`: 基础画像信息
- `province/city`: 地理位置信息，支持地域化运营

**状态管理字段**
- `status`: 用户状态(active/inactive/banned)，支持用户管理
- `last_login_at/last_login_ip`: 活跃度追踪

#### 索引设计说明
- **主键索引**: user_id自增主键，高效主键查询
- **唯一索引**: phone手机号唯一性约束
- **业务索引**: 
  - nickname支持昵称搜索
  - total_points支持积分排行
  - level支持等级查询
  - status支持状态筛选
- **时间索引**: created_at支持注册时间分析

#### 关联关系
- **一对多关系**: 与用户登录记录、评测报告、线索、积分记录等多个表建立关联
- **多对多关系**: 通过ct_user_relations表实现用户间的关注、好友等关系
- **外键策略**: 大部分关联使用ON DELETE CASCADE，用户删除时清理相关数据

---

### 3. ct_app_categories (APP分类表)

#### 业务用途
APP分类表管理应用的分类体系，支持层级分类结构，为APP的组织、展示、搜索提供分类基础。

#### 关键字段设计考虑

**主键设计**
- `category_id`: INT自增主键，分类数量相对有限

**分类标识字段**
- `category_name`: 分类显示名称，面向用户展示
- `category_code`: 分类代码，面向程序使用，设置唯一约束

**层级结构字段**
- `parent_id`: 父分类ID，支持无限层级分类
- `sort_order`: 排序字段，控制分类显示顺序

**扩展字段**
- `category_icon`: 分类图标URL，支持图标化展示
- `is_active`: 分类状态，支持分类的启用/禁用

#### 索引设计说明
- **主键索引**: category_id自增主键
- **唯一索引**: category_code确保分类代码唯一性
- **层级索引**: parent_id支持层级查询
- **排序索引**: sort_order支持排序查询
- **状态索引**: is_active支持活跃分类查询

#### 关联关系
- **自关联**: parent_id实现分类的层级结构
- **一对多关系**: 与ct_apps表建立关联，一个分类包含多个APP
- **外键策略**: 自关联使用ON DELETE SET NULL，APP关联使用ON DELETE RESTRICT

---

### 4. ct_file_categories (文件分类表)

#### 业务用途
文件分类表管理系统中所有文件的分类，为不同类型文件设置不同的上传限制和管理策略。

#### 关键字段设计考虑

**分类控制字段**
- `max_file_size`: 该分类允许的最大文件大小，精确控制上传限制
- `allowed_extensions`: 允许的文件扩展名，格式验证的基础
- `description`: 分类描述，说明该分类的用途和限制

**层级结构**
- 与APP分类表类似的层级设计，支持文件分类的细分

#### 索引设计说明
- 与APP分类表类似的索引策略
- 重点优化分类代码和状态查询

#### 关联关系
- **自关联**: 支持文件分类的层级结构
- **一对多关系**: 与ct_files表建立关联

---

## 第二层：依赖基础表详细设计

### 5. ct_user_logins (用户登录记录表)

#### 业务用途
记录用户的所有登录行为，支持安全审计、用户行为分析、异常登录检测等功能。

#### 关键字段设计考虑

**登录方式字段**
- `login_type`: 登录方式枚举，支持密码、短信、第三方登录等多种方式
- `login_status`: 登录结果，区分成功和失败的登录尝试
- `failure_reason`: 失败原因，便于问题诊断

**设备信息字段**
- `login_ip`: 登录IP地址，支持地理位置分析和异常检测
- `login_device`: 设备信息，支持设备管理
- `user_agent`: 浏览器信息，技术分析使用

**会话管理字段**
- `session_id`: 会话标识，支持会话管理
- `login_duration`: 登录时长，用户活跃度分析
- `logout_at`: 登出时间，完整的会话记录

#### 索引设计说明
- **外键索引**: user_id支持按用户查询登录历史
- **IP索引**: login_ip支持IP分析和异常检测
- **状态索引**: login_status支持成功/失败统计
- **时间索引**: created_at支持时间序列分析
- **会话索引**: session_id支持会话查询

#### 关联关系
- **多对一关系**: 与ct_users表关联，一个用户有多条登录记录
- **外键策略**: ON DELETE CASCADE，用户删除时清理登录记录

---

### 6. ct_user_relations (用户关系表)

#### 业务用途
管理用户之间的各种关系，包括关注、好友、拉黑、邀请等，支持社交功能的实现。

#### 关键字段设计考虑

**关系定义字段**
- `user_id/related_user_id`: 关系的两端用户
- `relation_type`: 关系类型枚举，支持多种用户关系
- `status`: 关系状态，支持待确认、已接受、已拒绝等状态

**关系类型设计**
- `follow`: 关注关系，单向关系
- `friend`: 好友关系，双向关系
- `block`: 拉黑关系，单向关系
- `invite`: 邀请关系，临时关系

#### 索引设计说明
- **外键索引**: user_id和related_user_id支持双向查询
- **关系索引**: relation_type支持按关系类型查询
- **状态索引**: status支持按状态筛选
- **唯一约束**: (user_id, related_user_id, relation_type)防止重复关系

#### 关联关系
- **多对一关系**: 与ct_users表的双重关联
- **外键策略**: ON DELETE CASCADE，用户删除时清理相关关系

---

### 7. ct_apps (APP信息表)

#### 业务用途
存储所有APP应用的详细信息，是评测系统和线索系统的核心数据基础。

#### 关键字段设计考虑

**应用标识字段**
- `app_name`: 应用名称，主要展示字段
- `app_package`: 应用包名，技术标识
- `app_version`: 应用版本，支持版本管理

**应用详情字段**
- `developer`: 开发商信息
- `app_size`: 应用大小，用户关心的信息
- `download_url`: 下载链接，功能性字段
- `logo_url`: Logo图片，展示需要

**富媒体字段**
- `description`: 应用描述，TEXT类型支持长文本
- `features`: JSON格式存储应用特性，灵活的结构化数据
- `screenshots`: JSON格式存储截图列表，支持多张截图

**统计字段**
- `rating/rating_count`: 评分和评分人数，用户决策参考
- `download_count/view_count`: 下载和查看统计，热度指标

**状态管理字段**
- `status`: 应用状态，支持审核流程
- `is_featured`: 推荐标识，运营功能

#### 索引设计说明
- **分类索引**: category_id支持按分类查询
- **名称索引**: app_name支持名称搜索
- **包名索引**: app_package支持技术查询
- **评分索引**: rating支持按评分排序
- **统计索引**: download_count支持热度排序
- **状态索引**: status和is_featured支持状态筛选

#### 关联关系
- **多对一关系**: 与ct_app_categories表关联
- **一对多关系**: 与评测报告、线索等表关联
- **外键策略**: 分类使用RESTRICT防止误删，其他使用CASCADE

---

### 8. ct_files (文件信息表)

#### 业务用途
统一管理系统中所有上传的文件，包括图片、文档等，提供完整的文件生命周期管理。

#### 关键字段设计考虑

**文件标识字段**
- `original_name`: 用户上传时的原始文件名，保留用户意图
- `stored_name`: 系统存储的文件名，通常使用UUID避免冲突
- `file_path/file_url`: 存储路径和访问URL，支持不同存储策略

**文件属性字段**
- `file_size`: 文件大小，存储管理需要
- `file_type`: MIME类型，文件类型识别
- `file_extension`: 文件扩展名，快速类型判断
- `file_hash`: MD5哈希值，支持文件去重和完整性校验

**媒体属性字段**
- `width/height`: 图片尺寸，图片处理需要
- `duration`: 视频/音频时长，媒体文件属性

**上传信息字段**
- `upload_ip/upload_device`: 上传环境信息，安全审计
- `storage_type`: 存储类型，支持多种存储方案

**状态管理字段**
- `is_public`: 访问权限控制
- `is_deleted/deleted_at`: 软删除机制，重要文件的保护

#### 索引设计说明
- **分类索引**: category_id支持按分类查询
- **用户索引**: uploader_id支持按上传者查询
- **哈希索引**: file_hash支持去重查询
- **类型索引**: file_type和file_extension支持按类型查询
- **存储索引**: storage_type支持按存储方式查询
- **状态索引**: is_public和is_deleted支持状态筛选
- **复合索引**: (uploader_id, category_id)支持用户分类查询

#### 关联关系
- **多对一关系**: 与ct_file_categories和ct_users表关联
- **一对多关系**: 与ct_file_relations表关联，支持多态关联
- **外键策略**: 使用RESTRICT防止误删除有关联的文件

---

## 第三层：业务表详细设计

### 9. ct_evaluation_reports (评测报告表)

#### 业务用途
存储用户提交的APP评测报告，是评测系统的核心数据表，支持评测内容管理、审核流程、积分奖励等功能。

#### 关键字段设计考虑

**报告内容字段**
- `report_title`: 报告标题，用户自定义
- `report_content`: 报告正文，TEXT类型支持长文本
- `task_description`: 任务描述，评测任务的具体要求

**评测数据字段**
- `completion_time`: 完成时长，任务执行效率指标
- `difficulty_level`: 难度等级，任务分类标准
- `rating`: 用户对APP的评分，1-5分制

**评测分析字段**
- `pros/cons/suggestions`: 优缺点和建议，结构化的评测分析
- `screenshots`: JSON格式存储截图列表，证明材料

**激励字段**
- `reward_points`: 奖励积分，激励机制
- `view_count/like_count`: 社交统计，内容质量指标
- `is_featured`: 推荐标识，优质内容推广

**流程管理字段**
- `status`: 报告状态，支持草稿、提交、审核等流程
- `submitted_at/approved_at`: 时间节点，流程追踪

#### 索引设计说明
- **应用索引**: app_id支持按APP查询评测
- **用户索引**: user_id支持按用户查询评测
- **评分索引**: rating支持按评分筛选
- **状态索引**: status支持按状态查询
- **推荐索引**: is_featured支持推荐内容查询
- **时间索引**: submitted_at支持时间序列查询
- **复合索引**: 
  - (app_id, user_id)支持用户对特定APP的评测查询
  - (status, submitted_at)支持审核队列查询

#### 关联关系
- **多对一关系**: 与ct_apps和ct_users表关联
- **一对多关系**: 与ct_evaluation_details表关联，支持详细数据
- **外键策略**: ON DELETE CASCADE，APP或用户删除时清理评测

---

### 10. ct_water_clues (放水线索表)

#### 业务用途
存储用户分享的放水线索信息，是线索系统的核心表，支持线索管理、效果追踪、收益分析等功能。

#### 关键字段设计考虑

**线索内容字段**
- `clue_title/clue_content`: 线索标题和详细内容
- `clue_type`: 线索类型，支持bug、漏洞、推广等分类
- `steps`: 操作步骤，TEXT类型存储详细步骤

**收益分析字段**
- `expected_reward/actual_reward`: 预期和实际收益，效果对比
- `success_rate`: 成功率，线索质量指标
- `risk_level`: 风险等级，风险提示

**统计字段**
- `try_count/success_count`: 尝试和成功次数，效果统计
- `view_count/like_count`: 浏览和点赞，内容热度

**时效管理字段**
- `expires_at`: 过期时间，线索时效性管理
- `status`: 线索状态，包含过期状态

#### 索引设计说明
- 与评测报告表类似的索引策略
- 额外的过期时间索引支持时效性管理
- 风险等级索引支持风险筛选

#### 关联关系
- **多对一关系**: 与ct_apps和ct_users表关联
- **一对多关系**: 与线索反馈和统计表关联
- **外键策略**: ON DELETE CASCADE

---

## 索引性能优化总体策略

### 1. 主键索引优化
- **自增主键**: 所有表使用自增主键，保证插入性能
- **BIGINT类型**: 大数据量表使用BIGINT，避免主键溢出
- **聚簇索引**: InnoDB引擎的聚簇索引特性，优化范围查询

### 2. 外键索引优化
- **自动创建**: 所有外键字段自动创建索引
- **级联策略**: 合理的级联删除和更新策略
- **性能考虑**: 外键约束对写入性能的影响评估

### 3. 业务索引优化
- **高频查询**: 为高频查询字段创建单列索引
- **复合查询**: 为多条件查询创建复合索引
- **覆盖索引**: 尽可能使用覆盖索引减少回表

### 4. 复合索引设计原则
- **选择性原则**: 高选择性字段放在前面
- **查询模式**: 根据实际查询模式设计索引顺序
- **索引长度**: 控制复合索引的字段数量，避免过长

### 5. 时间索引优化
- **分区支持**: 时间字段索引支持表分区
- **范围查询**: 优化时间范围查询性能
- **统计分析**: 支持时间维度的统计分析

---

## 表关联关系总览

### 核心关联路径
```
用户体系:
ct_users → ct_user_logins (登录记录)
ct_users → ct_user_relations (用户关系)
ct_users → ct_point_records (积分记录)

应用体系:
ct_app_categories → ct_apps (应用分类)
ct_apps → ct_evaluation_reports (评测报告)
ct_apps → ct_water_clues (放水线索)

文件体系:
ct_file_categories → ct_files (文件分类)
ct_files → ct_file_relations (文件关联)

审核体系:
ct_audit_rules → ct_content_audits (审核规则)
ct_content_audits → ct_audit_logs (审核日志)

系统管理:
ct_admin_users → ct_system_configs (配置管理)
ct_admin_users → ct_content_audits (审核员)
```

### 多态关联设计
- **文件关联**: ct_file_relations通过business_type和business_id实现多态关联
- **内容审核**: ct_content_audits通过content_type和content_id实现多态关联
- **积分记录**: ct_point_records通过source_type和source_id实现多态关联

---

## 性能监控建议

### 1. 慢查询监控
- 定期分析慢查询日志
- 识别需要优化的查询模式
- 调整索引策略

### 2. 索引使用分析
- 监控索引的使用频率
- 识别冗余索引
- 优化索引覆盖率

### 3. 表大小监控
- 监控表的增长速度
- 规划分区策略
- 制定归档计划

### 4. 锁竞争分析
- 监控表锁和行锁竞争
- 优化事务设计
- 调整并发策略

---

## 扩展性考虑

### 1. 水平扩展
- **分库分表**: 用户相关表支持按用户ID分片
- **读写分离**: 索引设计支持读写分离架构
- **缓存友好**: 热点数据的缓存策略

### 2. 垂直扩展
- **存储引擎**: 根据业务特点选择合适的存储引擎
- **硬件优化**: SSD存储和内存配置优化
- **参数调优**: MySQL参数的针对性调优

### 3. 功能扩展
- **新业务模块**: 预留扩展字段和关联设计
- **数据迁移**: 支持平滑的数据迁移和升级
- **版本兼容**: 保持向后兼容性

---

## 总结

次推应用数据库表结构设计遵循现代数据库设计的最佳实践，通过分层架构、合理的索引策略、完整的约束体系，为应用提供了高性能、高可靠性的数据基础。设计充分考虑了业务需求、性能优化、安全性和扩展性，能够支撑应用的长期发展需要。