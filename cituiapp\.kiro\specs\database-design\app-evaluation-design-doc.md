# APP和评测相关表结构设计说明文档

## 概述

本文档详细说明了次推（CitUI）应用中APP和评测管理系统的数据库表结构设计。设计基于需求文档中的需求2（APP评测管理系统），涵盖APP信息管理、分类管理、评测报告管理和评测数据详情管理四个核心功能模块。

## 表结构设计

### 1. ct_app_categories (APP分类表)

#### 业务用途
- 管理APP的分类信息，支持APP按类型进行组织和筛选
- 为用户提供分类浏览功能，提升用户体验
- 支持分类的动态管理和排序

#### 关键字段说明
- `name`: 分类名称，如"游戏类"、"任务类"、"购物类"等
- `icon`: 分类图标URL，用于前端展示
- `sort_order`: 排序权重，数值越大越靠前显示
- `status`: 分类状态，支持启用/禁用切换

#### 设计考虑
- 支持分类的灵活排序，便于运营调整
- 预留图标字段，支持更丰富的UI展示
- 软删除机制，通过status字段控制显示

### 2. ct_apps (APP信息表)

#### 业务用途
- 存储APP的完整信息，包括基础信息、下载信息、评分统计等
- 支持APP的分类管理和状态控制
- 为评测报告提供APP基础数据支撑

#### 关键字段说明
- `category_id`: 关联分类表，支持APP分类管理
- `logo_url`: APP Logo存储路径
- `rating/rating_count`: 评分统计，支持平均评分计算
- `download_count`: 下载统计，用于热度排序
- `run_mode`: 运行模式（挂机、手动、半自动），重要的筛选条件
- `newcomer_benefit`: 新人福利描述，吸引用户的重要信息
- `withdraw_threshold`: 提现门槛，用户关心的核心数据
- `guarantee_amount`: 顶包金额，风险评估指标
- `is_hot/is_recommended`: 热门和推荐标记，支持运营推广
- `is_water_available`: 放水状态标记，与线索功能关联

#### 设计考虑
- 评分采用decimal(3,2)类型，支持0-5分精确到小数点后两位
- 金额字段使用decimal类型，避免浮点数精度问题
- 多个布尔标记字段，支持灵活的筛选和排序
- 状态字段支持正常、下架、审核中三种状态

### 3. ct_evaluation_reports (评测报告表)

#### 业务用途
- 存储用户提交的完整评测报告信息
- 支持评测报告的审核流程管理
- 记录评测的详细数据和用户体验

#### 关键字段说明
- `app_id/user_id`: 关联APP和用户，建立评测关系
- `rating`: 用户给出的评分，用于计算APP平均评分
- `test_count/total_earnings/test_duration`: 核心测试数据
- `test_device`: 测试设备信息，重要的参考数据
- `evaluator_name/evaluation_date`: 测评人和日期信息
- `earnings_detail`: JSON格式存储收益明细，支持结构化数据
- `report_content`: 测评报告的文字内容
- `audit_status/audit_user_id/audit_time`: 完整的审核流程记录
- `points_awarded`: 积分奖励记录，与积分系统关联
- `view_count/like_count`: 互动数据统计

#### 设计考虑
- 审核流程完整，支持待审核、通过、拒绝三种状态
- 使用JSON字段存储收益明细，支持灵活的数据结构
- 预留互动统计字段，支持社交功能扩展
- 软删除设计，重要数据不物理删除

### 4. ct_evaluation_details (评测数据详情表)

#### 业务用途
- 存储评测报告的详细数据和附件信息
- 支持多种类型的详情数据管理
- 为评测报告提供丰富的展示内容

#### 关键字段说明
- `report_id`: 关联评测报告，建立主从关系
- `detail_type`: 详情类型，支持截图、收益明细、测试数据等
- `content`: 详情的文字内容
- `file_url`: 相关文件的存储路径
- `sort_order`: 详情的显示排序
- `extra_data`: JSON格式的扩展数据

#### 设计考虑
- 通过detail_type字段支持多种详情类型
- 级联删除设计，删除报告时自动删除相关详情
- JSON扩展字段，支持不同类型详情的个性化数据
- 排序字段，支持详情的自定义排列

## 表关系设计

### 主要关联关系
1. **ct_apps ← ct_app_categories**: APP属于某个分类（多对一）
2. **ct_evaluation_reports ← ct_apps**: 评测报告关联APP（多对一）
3. **ct_evaluation_reports ← ct_users**: 评测报告关联用户（多对一）
4. **ct_evaluation_details ← ct_evaluation_reports**: 详情关联报告（多对一）

### 外键约束策略
- APP分类删除时，需要先处理相关APP
- APP删除时，相关评测报告保留但标记APP已下架
- 评测报告删除时，相关详情数据级联删除
- 用户删除时，评测报告保留但标记用户已注销

## 索引设计策略

### 查询场景分析
1. **APP列表查询**: 按分类、状态、热门度、评分排序
2. **评测报告查询**: 按APP、用户、时间、审核状态查询
3. **详情数据查询**: 按报告ID和类型查询
4. **统计查询**: 按时间段统计评测数量、评分分布等

### 索引设计原则
- 高频查询字段建立单列索引
- 组合查询条件建立复合索引
- 排序字段考虑索引覆盖
- 外键字段自动建立索引

## 性能优化考虑

### 查询优化
1. **分页查询**: 使用LIMIT和OFFSET，配合适当索引
2. **统计查询**: 考虑使用汇总表或缓存机制
3. **全文搜索**: APP名称和报告内容支持全文索引
4. **读写分离**: 评测数据读多写少，适合读写分离

### 存储优化
1. **JSON字段**: 合理使用JSON字段存储非结构化数据
2. **文件存储**: 大文件使用对象存储，数据库只存储URL
3. **历史数据**: 考虑按时间分表或归档策略
4. **缓存策略**: 热门APP和评测数据使用Redis缓存

## 数据完整性保证

### 约束设计
1. **非空约束**: 关键业务字段设置NOT NULL
2. **唯一约束**: 防止重复数据
3. **外键约束**: 保证关联数据一致性
4. **检查约束**: 验证数据有效性（如评分范围）

### 事务处理
1. **评测提交**: 报告和详情数据在同一事务中处理
2. **审核流程**: 审核状态变更和积分发放在同一事务
3. **统计更新**: APP评分更新使用事务保证一致性
4. **文件关联**: 文件上传和数据库记录同步处理

## 扩展性设计

### 预留扩展
1. **状态扩展**: 状态字段支持更多业务状态
2. **分类扩展**: 支持多级分类和标签系统
3. **评分扩展**: 支持多维度评分（如界面、收益、稳定性）
4. **互动扩展**: 支持评论、点赞、分享等社交功能

### 版本兼容
1. **字段添加**: 新增字段设置默认值，保证兼容性
2. **数据迁移**: 提供数据迁移脚本和回滚方案
3. **API兼容**: 数据库变更不影响现有API接口
4. **配置化**: 业务规则通过配置表管理，减少代码变更

## 安全性考虑

### 数据安全
1. **敏感数据**: 用户隐私数据考虑加密存储
2. **SQL注入**: 使用参数化查询防止注入攻击
3. **权限控制**: 数据库用户权限最小化原则
4. **审计日志**: 重要操作记录审计日志

### 业务安全
1. **防刷机制**: 限制用户提交频率和数量
2. **内容审核**: 评测内容自动和人工审核结合
3. **积分安全**: 积分发放和扣除有完整的审计记录
4. **数据备份**: 定期备份和灾难恢复方案

## 监控和维护

### 性能监控
1. **慢查询**: 监控和优化慢查询
2. **索引使用**: 分析索引使用情况和效果
3. **存储空间**: 监控表空间增长趋势
4. **并发性能**: 监控高并发场景下的性能表现

### 数据质量
1. **数据一致性**: 定期检查关联数据一致性
2. **数据完整性**: 验证关键业务数据完整性
3. **异常数据**: 识别和处理异常数据
4. **数据清理**: 定期清理无效和过期数据

## 总结

本设计方案充分考虑了次推应用APP评测功能的业务需求，通过合理的表结构设计、索引优化、约束设置等手段，确保了数据的完整性、一致性和高性能。同时预留了足够的扩展空间，支持业务的持续发展和功能迭代。