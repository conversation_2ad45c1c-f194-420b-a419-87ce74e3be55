# CitUI Laravel后端API系统

## 项目概述

CitUI是一个APP评测和线索分享平台的Laravel后端API系统，为前端uni-app应用提供完整的数据支持和业务逻辑处理。

## 功能模块

### 核心功能
- **用户认证系统**：注册、登录、Token管理
- **用户管理**：个人信息、头像上传、关系管理
- **APP管理**：APP信息、分类、搜索、推荐
- **评测系统**：评测报告提交、审核、展示
- **线索系统**：放水线索发布、反馈、统计
- **积分系统**：积分规则、奖励兑换、记录管理
- **文件管理**：文件上传、存储、关联管理
- **内容审核**：自动审核、人工审核、审核流程
- **系统管理**：配置管理、统计分析、日志记录

## 技术架构

### 技术栈
- **框架**：Laravel 9.x
- **数据库**：MySQL 5.7+
- **认证**：Laravel Sanctum
- **缓存**：Redis
- **文件存储**：本地存储 / 云存储（OSS/COS）
- **队列**：Redis Queue

### 架构设计
```
├── 控制器层 (Controllers/Citui/)
├── 服务层 (Services/Citui/)
├── 模型层 (Models/Citui/)
├── 请求验证层 (Requests/Citui/)
└── 中间件层 (Middleware/)
```

## 目录结构

```
app/
├── Http/Controllers/Citui/     # CitUI控制器
│   ├── AuthController.php      # 认证控制器
│   ├── UserController.php      # 用户管理控制器
│   ├── AppController.php       # APP管理控制器
│   ├── EvaluationController.php # 评测系统控制器
│   ├── ClueController.php      # 线索系统控制器
│   ├── PointController.php     # 积分系统控制器
│   ├── FileController.php      # 文件管理控制器
│   └── AdminController.php     # 管理后台控制器
├── Models/Citui/               # CitUI模型
│   ├── BaseCituiModel.php      # 基础模型类
│   ├── User.php                # 用户模型
│   ├── App.php                 # APP模型
│   └── ...                     # 其他模型
├── Services/Citui/             # CitUI服务层
│   ├── BaseCituiService.php    # 基础服务类
│   ├── AuthService.php         # 认证服务
│   ├── UserService.php         # 用户服务
│   └── ...                     # 其他服务
├── Http/Requests/Citui/        # 请求验证类
│   └── BaseRequest.php         # 基础请求类
├── Http/Middleware/            # 中间件
│   └── CituiAuthMiddleware.php # CitUI认证中间件
└── ...
```

## API接口

### 基础URL
```
开发环境: http://localhost:8000/api/citui
生产环境: https://api.citui.com/api/citui
```

### 响应格式
```json
{
    "status": 200,
    "code": 1,
    "msg": "success",
    "data": {}
}
```

### 主要接口分组
- `/auth/*` - 认证相关接口
- `/user/*` - 用户管理接口
- `/apps/*` - APP管理接口
- `/evaluations/*` - 评测系统接口
- `/clues/*` - 线索系统接口
- `/points/*` - 积分系统接口
- `/files/*` - 文件管理接口
- `/admin/*` - 管理后台接口

## 配置说明

### 环境变量
```env
# CitUI应用配置
CITUI_APP_NAME=次推
CITUI_STORAGE_DISK=local
CITUI_SMS_PROVIDER=aliyun
CITUI_SMS_TEMPLATE_ID=
CITUI_SMS_SIGN_NAME=次推
```

### 配置文件
- `config/citui.php` - CitUI应用主配置文件
- 包含分页、文件上传、积分、审核、缓存等配置

## 开发规范

### 代码风格
- 严格遵循PSR-12编码规范
- 使用强类型声明 `declare(strict_types=1)`
- 所有类和方法都要有完整的注释

### 命名规范
- 控制器：`XxxController`
- 服务类：`XxxService`
- 模型类：`Xxx`
- 请求类：`XxxRequest`

### 响应格式
- 成功：使用 `apiSuccess()` 方法
- 失败：使用 `apiError()` 方法
- 保持与现有项目一致的响应格式

## 部署说明

### 环境要求
- PHP 8.0+
- MySQL 5.7+
- Redis 5.0+
- Composer 2.0+

### 部署步骤
1. 克隆代码到服务器
2. 安装依赖：`composer install`
3. 配置环境变量
4. 执行数据库SQL文件创建表结构
5. 配置Web服务器
6. 启动队列进程

## 开发状态

当前项目处于基础架构搭建阶段，主要完成了：

✅ **已完成**
- [x] 项目目录结构创建
- [x] 基础控制器框架
- [x] 基础服务层框架
- [x] 基础模型框架
- [x] 路由配置
- [x] 中间件配置
- [x] 配置文件

🚧 **待实现**
- [ ] 数据库表结构SQL文件
- [ ] 具体业务逻辑实现
- [ ] 数据验证规则
- [ ] 单元测试
- [ ] API文档

## 联系信息

如有问题，请联系开发团队。