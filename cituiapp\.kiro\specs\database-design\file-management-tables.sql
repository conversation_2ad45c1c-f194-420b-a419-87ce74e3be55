-- =============================================
-- 文件管理系统表结构设计
-- 创建时间: 2025-01-08
-- 描述: 支持次推应用的文件上传、存储和管理功能
-- =============================================

-- 1. 文件分类表 (ct_file_categories)
-- 用途: 管理文件分类，支持不同类型文件的分类管理
CREATE TABLE ct_file_categories (
    category_id INT PRIMARY KEY AUTO_INCREMENT COMMENT '分类ID',
    category_name VARCHAR(50) NOT NULL COMMENT '分类名称',
    category_code VARCHAR(20) NOT NULL UNIQUE COMMENT '分类代码',
    parent_id INT DEFAULT NULL COMMENT '父分类ID',
    sort_order INT DEFAULT 0 COMMENT '排序顺序',
    max_file_size BIGINT DEFAULT 10485760 COMMENT '最大文件大小(字节)，默认10MB',
    allowed_extensions VARCHAR(200) DEFAULT 'jpg,jpeg,png,gif,webp' COMMENT '允许的文件扩展名',
    description VARCHAR(200) DEFAULT NULL COMMENT '分类描述',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否启用(0:禁用 1:启用)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引设计
    INDEX idx_category_code (category_code),
    INDEX idx_parent_id (parent_id),
    INDEX idx_sort_order (sort_order),
    INDEX idx_is_active (is_active),
    
    -- 外键约束
    FOREIGN KEY (parent_id) REFERENCES ct_file_categories(category_id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文件分类表';

-- 2. 文件信息表 (ct_files)
-- 用途: 存储所有上传文件的基本信息和元数据
CREATE TABLE ct_files (
    file_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '文件ID',
    category_id INT NOT NULL COMMENT '文件分类ID',
    uploader_id BIGINT NOT NULL COMMENT '上传用户ID',
    original_name VARCHAR(255) NOT NULL COMMENT '原始文件名',
    stored_name VARCHAR(255) NOT NULL COMMENT '存储文件名(UUID)',
    file_path VARCHAR(500) NOT NULL COMMENT '文件存储路径',
    file_url VARCHAR(500) NOT NULL COMMENT '文件访问URL',
    file_size BIGINT NOT NULL COMMENT '文件大小(字节)',
    file_type VARCHAR(100) NOT NULL COMMENT '文件MIME类型',
    file_extension VARCHAR(10) NOT NULL COMMENT '文件扩展名',
    file_hash VARCHAR(64) NOT NULL COMMENT '文件MD5哈希值',
    width INT DEFAULT NULL COMMENT '图片宽度(像素)',
    height INT DEFAULT NULL COMMENT '图片高度(像素)',
    duration INT DEFAULT NULL COMMENT '视频/音频时长(秒)',
    upload_ip VARCHAR(45) DEFAULT NULL COMMENT '上传IP地址',
    upload_device VARCHAR(100) DEFAULT NULL COMMENT '上传设备信息',
    storage_type ENUM('local', 'oss', 'cos', 'qiniu') DEFAULT 'local' COMMENT '存储类型',
    is_public TINYINT(1) DEFAULT 1 COMMENT '是否公开访问(0:私有 1:公开)',
    is_deleted TINYINT(1) DEFAULT 0 COMMENT '是否已删除(0:正常 1:已删除)',
    deleted_at TIMESTAMP NULL DEFAULT NULL COMMENT '删除时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引设计
    INDEX idx_category_id (category_id),
    INDEX idx_uploader_id (uploader_id),
    INDEX idx_file_hash (file_hash),
    INDEX idx_file_type (file_type),
    INDEX idx_file_extension (file_extension),
    INDEX idx_storage_type (storage_type),
    INDEX idx_is_public (is_public),
    INDEX idx_is_deleted (is_deleted),
    INDEX idx_created_at (created_at),
    INDEX idx_file_size (file_size),
    
    -- 复合索引
    INDEX idx_uploader_category (uploader_id, category_id),
    INDEX idx_deleted_created (is_deleted, created_at),
    
    -- 外键约束
    FOREIGN KEY (category_id) REFERENCES ct_file_categories(category_id) ON DELETE RESTRICT,
    FOREIGN KEY (uploader_id) REFERENCES ct_users(user_id) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文件信息表';

-- 3. 文件关联表 (ct_file_relations)
-- 用途: 建立文件与业务对象的多对多关联关系
CREATE TABLE ct_file_relations (
    relation_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '关联ID',
    file_id BIGINT NOT NULL COMMENT '文件ID',
    business_type VARCHAR(50) NOT NULL COMMENT '业务类型(app_logo,evaluation_screenshot,clue_screenshot等)',
    business_id BIGINT NOT NULL COMMENT '业务对象ID',
    relation_type VARCHAR(20) DEFAULT 'attachment' COMMENT '关联类型(attachment:附件,cover:封面,thumbnail:缩略图)',
    sort_order INT DEFAULT 0 COMMENT '排序顺序',
    is_primary TINYINT(1) DEFAULT 0 COMMENT '是否主要文件(0:否 1:是)',
    description VARCHAR(200) DEFAULT NULL COMMENT '关联描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引设计
    INDEX idx_file_id (file_id),
    INDEX idx_business_type (business_type),
    INDEX idx_business_id (business_id),
    INDEX idx_relation_type (relation_type),
    INDEX idx_sort_order (sort_order),
    INDEX idx_is_primary (is_primary),
    
    -- 复合索引
    INDEX idx_business_type_id (business_type, business_id),
    INDEX idx_file_business (file_id, business_type, business_id),
    INDEX idx_business_primary (business_type, business_id, is_primary),
    
    -- 唯一约束
    UNIQUE KEY uk_file_business_relation (file_id, business_type, business_id, relation_type),
    
    -- 外键约束
    FOREIGN KEY (file_id) REFERENCES ct_files(file_id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文件关联表';

-- =============================================
-- 初始化数据
-- =============================================

-- 插入文件分类基础数据
INSERT INTO ct_file_categories (category_name, category_code, parent_id, sort_order, max_file_size, allowed_extensions, description) VALUES
('APP图标', 'app_logo', NULL, 1, 2097152, 'jpg,jpeg,png,webp', 'APP应用图标文件'),
('评测截图', 'evaluation_screenshot', NULL, 2, 5242880, 'jpg,jpeg,png,webp', '评测报告相关截图'),
('线索截图', 'clue_screenshot', NULL, 3, 5242880, 'jpg,jpeg,png,webp', '放水线索相关截图'),
('用户头像', 'user_avatar', NULL, 4, 1048576, 'jpg,jpeg,png,webp', '用户头像图片'),
('系统图片', 'system_image', NULL, 5, 10485760, 'jpg,jpeg,png,gif,webp', '系统使用的图片资源'),
('文档附件', 'document', NULL, 6, 52428800, 'pdf,doc,docx,xls,xlsx,txt', '文档类附件'),
('其他文件', 'other', NULL, 99, 10485760, 'jpg,jpeg,png,gif,webp,pdf,txt', '其他类型文件');

-- =============================================
-- 触发器设计
-- =============================================

-- 文件删除触发器：软删除文件时更新删除时间
DELIMITER $$
CREATE TRIGGER tr_files_soft_delete 
BEFORE UPDATE ON ct_files
FOR EACH ROW
BEGIN
    IF NEW.is_deleted = 1 AND OLD.is_deleted = 0 THEN
        SET NEW.deleted_at = CURRENT_TIMESTAMP;
    ELSEIF NEW.is_deleted = 0 AND OLD.is_deleted = 1 THEN
        SET NEW.deleted_at = NULL;
    END IF;
END$$
DELIMITER ;

-- 文件关联清理触发器：文件被软删除时清理关联关系
DELIMITER $$
CREATE TRIGGER tr_files_relation_cleanup
AFTER UPDATE ON ct_files
FOR EACH ROW
BEGIN
    IF NEW.is_deleted = 1 AND OLD.is_deleted = 0 THEN
        DELETE FROM ct_file_relations WHERE file_id = NEW.file_id;
    END IF;
END$$
DELIMITER ;