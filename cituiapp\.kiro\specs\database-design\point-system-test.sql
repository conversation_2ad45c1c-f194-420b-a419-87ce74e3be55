-- =============================================
-- 积分和奖励系统测试SQL脚本
-- 测试目的: 验证表结构正确性和业务逻辑完整性
-- =============================================

-- 清理测试数据
SET FOREIGN_KEY_CHECKS = 0;
DELETE FROM ct_point_exchanges WHERE user_id IN (1001, 1002);
DELETE FROM ct_point_records WHERE user_id IN (1001, 1002);
DELETE FROM ct_user_points WHERE user_id IN (1001, 1002);
DELETE FROM ct_point_rules WHERE rule_code LIKE 'test_%';
DELETE FROM ct_reward_configs WHERE config_code LIKE 'test_%';
SET FOREIGN_KEY_CHECKS = 1;

-- =============================================
-- 1. 测试积分规则配置
-- =============================================

-- 插入测试积分规则
INSERT INTO ct_point_rules (rule_code, rule_name, rule_description, point_value, trigger_condition, daily_limit, status) VALUES
('test_evaluation_submit', '评测提交奖励', '用户提交APP评测报告获得积分', 50, '提交评测报告', 5, 1),
('test_clue_submit', '线索提交奖励', '用户提交放水线索获得积分', 30, '提交放水线索', 10, 1),
('test_daily_login', '每日登录奖励', '用户每日首次登录获得积分', 10, '每日首次登录', 1, 1),
('test_audit_pass', '审核通过奖励', '内容审核通过额外奖励', 20, '内容审核通过', NULL, 1),
('test_audit_reject', '审核拒绝扣除', '内容审核拒绝扣除积分', -50, '内容审核拒绝', NULL, 1);

-- 验证积分规则插入
SELECT '=== 积分规则测试 ===' as test_section;
SELECT rule_code, rule_name, point_value, daily_limit, status 
FROM ct_point_rules 
WHERE rule_code LIKE 'test_%'
ORDER BY point_value DESC;

-- =============================================
-- 2. 测试奖励配置
-- =============================================

-- 插入测试奖励配置
INSERT INTO ct_reward_configs (config_code, config_name, config_type, reward_type, reward_value, required_points, exchange_limit, stock_quantity, sort_order, status) VALUES
('test_point_100', '100积分奖励', 'point_exchange', 'point', '{"points": 100, "description": "额外100积分"}', 50, 10, NULL, 1, 1),
('test_virtual_gift', '虚拟礼品', 'point_exchange', 'virtual_item', '{"item_name": "专属头像框", "item_id": "avatar_001"}', 200, 1, 100, 2, 1),
('test_real_gift', '实物奖品', 'point_exchange', 'real_item', '{"item_name": "精美笔记本", "item_id": "notebook_001"}', 1000, 1, 10, 3, 1),
('test_level_reward', '等级奖励', 'level_reward', 'point', '{"points": 500, "description": "升级奖励"}', NULL, NULL, NULL, 4, 1);

-- 验证奖励配置插入
SELECT '=== 奖励配置测试 ===' as test_section;
SELECT config_code, config_name, reward_type, required_points, stock_quantity, status 
FROM ct_reward_configs 
WHERE config_code LIKE 'test_%'
ORDER BY sort_order;

-- =============================================
-- 3. 测试用户积分初始化
-- =============================================

-- 初始化测试用户积分
INSERT INTO ct_user_points (user_id, total_points, available_points, frozen_points, total_earned, total_consumed, level, level_progress) VALUES
(1001, 0, 0, 0, 0, 0, 1, 0),
(1002, 0, 0, 0, 0, 0, 1, 0);

-- 验证用户积分初始化
SELECT '=== 用户积分初始化测试 ===' as test_section;
SELECT user_id, total_points, available_points, level 
FROM ct_user_points 
WHERE user_id IN (1001, 1002);

-- =============================================
-- 4. 测试积分获取流程
-- =============================================

-- 模拟用户1001提交评测报告获得积分
INSERT INTO ct_point_records (user_id, point_change, point_balance, change_type, change_reason, related_type, related_id, status) VALUES
(1001, 50, 50, 'evaluation_submit', '提交APP评测报告', 'evaluation', 2001, 1);

-- 更新用户积分汇总
UPDATE ct_user_points 
SET total_points = 50, available_points = 50, total_earned = 50, last_point_time = NOW()
WHERE user_id = 1001;

-- 模拟用户1001提交放水线索获得积分
INSERT INTO ct_point_records (user_id, point_change, point_balance, change_type, change_reason, related_type, related_id, status) VALUES
(1001, 30, 80, 'clue_submit', '提交放水线索', 'clue', 3001, 1);

-- 更新用户积分汇总
UPDATE ct_user_points 
SET total_points = 80, available_points = 80, total_earned = 80, last_point_time = NOW()
WHERE user_id = 1001;

-- 模拟用户1002每日登录获得积分
INSERT INTO ct_point_records (user_id, point_change, point_balance, change_type, change_reason, status) VALUES
(1002, 10, 10, 'daily_login', '每日首次登录奖励', 1);

-- 更新用户积分汇总
UPDATE ct_user_points 
SET total_points = 10, available_points = 10, total_earned = 10, last_point_time = NOW()
WHERE user_id = 1002;

-- 验证积分获取记录
SELECT '=== 积分获取流程测试 ===' as test_section;
SELECT pr.user_id, pr.point_change, pr.point_balance, pr.change_type, pr.change_reason, pr.created_at
FROM ct_point_records pr
WHERE pr.user_id IN (1001, 1002)
ORDER BY pr.user_id, pr.created_at;

-- 验证用户积分汇总更新
SELECT user_id, total_points, available_points, total_earned 
FROM ct_user_points 
WHERE user_id IN (1001, 1002);

-- =============================================
-- 5. 测试审核积分发放流程
-- =============================================

-- 模拟审核通过，给予额外奖励
INSERT INTO ct_point_records (user_id, point_change, point_balance, change_type, change_reason, related_type, related_id, operator_id, status) VALUES
(1001, 20, 100, 'audit_pass', '评测报告审核通过额外奖励', 'evaluation', 2001, 9001, 1);

-- 更新用户积分汇总
UPDATE ct_user_points 
SET total_points = 100, available_points = 100, total_earned = 100, last_point_time = NOW()
WHERE user_id = 1001;

-- 模拟审核拒绝，扣除积分
INSERT INTO ct_point_records (user_id, point_change, point_balance, change_type, change_reason, related_type, related_id, operator_id, status) VALUES
(1002, -50, -40, 'audit_reject', '线索内容审核拒绝扣除积分', 'clue', 3002, 9001, 1);

-- 更新用户积分汇总（注意处理负积分）
UPDATE ct_user_points 
SET total_points = -40, available_points = 0, total_consumed = 50, last_point_time = NOW()
WHERE user_id = 1002;

-- 验证审核积分流程
SELECT '=== 审核积分流程测试 ===' as test_section;
SELECT pr.user_id, pr.point_change, pr.change_type, pr.change_reason, pr.operator_id
FROM ct_point_records pr
WHERE pr.change_type IN ('audit_pass', 'audit_reject')
ORDER BY pr.user_id;

-- =============================================
-- 6. 测试积分兑换流程
-- =============================================

-- 模拟用户1001兑换虚拟礼品（需要200积分，但用户只有100积分，应该失败）
-- 先检查用户积分是否足够
SELECT '=== 积分兑换前置检查 ===' as test_section;
SELECT 
    up.user_id,
    up.available_points,
    rc.required_points,
    CASE 
        WHEN up.available_points >= rc.required_points THEN '可以兑换'
        ELSE '积分不足'
    END as exchange_status
FROM ct_user_points up
CROSS JOIN ct_reward_configs rc
WHERE up.user_id = 1001 AND rc.config_code = 'test_virtual_gift';

-- 模拟用户1001兑换100积分奖励（需要50积分，用户有100积分，可以兑换）
INSERT INTO ct_point_exchanges (user_id, reward_config_id, exchange_points, reward_content, exchange_status) 
SELECT 1001, rc.id, rc.required_points, rc.reward_value, 0
FROM ct_reward_configs rc 
WHERE rc.config_code = 'test_point_100';

-- 扣除兑换积分，增加冻结积分
UPDATE ct_user_points 
SET available_points = available_points - 50, frozen_points = frozen_points + 50
WHERE user_id = 1001;

-- 模拟管理员处理兑换（兑换成功）
UPDATE ct_point_exchanges 
SET exchange_status = 1, process_time = NOW(), process_by = 9001
WHERE user_id = 1001 AND exchange_status = 0;

-- 处理成功后，扣除冻结积分，增加总消耗
UPDATE ct_user_points 
SET frozen_points = frozen_points - 50, total_consumed = total_consumed + 50
WHERE user_id = 1001;

-- 发放兑换奖励（100积分）
INSERT INTO ct_point_records (user_id, point_change, point_balance, change_type, change_reason, status) VALUES
(1001, 100, 150, 'exchange_reward', '积分兑换奖励', 1);

-- 更新用户积分汇总
UPDATE ct_user_points 
SET total_points = 150, available_points = available_points + 100, total_earned = total_earned + 100, last_point_time = NOW()
WHERE user_id = 1001;

-- 验证兑换流程
SELECT '=== 积分兑换流程测试 ===' as test_section;
SELECT pe.user_id, pe.exchange_points, pe.reward_content, pe.exchange_status, pe.process_time
FROM ct_point_exchanges pe
WHERE pe.user_id = 1001;

-- 验证兑换后用户积分状态
SELECT user_id, total_points, available_points, frozen_points, total_earned, total_consumed 
FROM ct_user_points 
WHERE user_id = 1001;

-- =============================================
-- 7. 测试数据完整性和约束
-- =============================================

SELECT '=== 数据完整性测试 ===' as test_section;

-- 测试积分记录完整性
SELECT 
    COUNT(*) as total_records,
    SUM(CASE WHEN point_change > 0 THEN 1 ELSE 0 END) as positive_records,
    SUM(CASE WHEN point_change < 0 THEN 1 ELSE 0 END) as negative_records,
    SUM(point_change) as total_point_change
FROM ct_point_records 
WHERE user_id IN (1001, 1002);

-- 测试用户积分汇总一致性
SELECT 
    up.user_id,
    up.total_points as summary_total,
    COALESCE(pr.calculated_total, 0) as calculated_total,
    CASE 
        WHEN up.total_points = COALESCE(pr.calculated_total, 0) THEN '一致'
        ELSE '不一致'
    END as consistency_check
FROM ct_user_points up
LEFT JOIN (
    SELECT user_id, SUM(point_change) as calculated_total
    FROM ct_point_records 
    WHERE status = 1
    GROUP BY user_id
) pr ON up.user_id = pr.user_id
WHERE up.user_id IN (1001, 1002);

-- =============================================
-- 8. 测试查询性能和索引效果
-- =============================================

SELECT '=== 查询性能测试 ===' as test_section;

-- 测试按用户查询积分记录（应该使用user_id索引）
EXPLAIN SELECT * FROM ct_point_records WHERE user_id = 1001 ORDER BY created_at DESC LIMIT 10;

-- 测试按变动类型查询（应该使用change_type索引）
EXPLAIN SELECT * FROM ct_point_records WHERE change_type = 'evaluation_submit';

-- 测试按关联业务查询（应该使用复合索引）
EXPLAIN SELECT * FROM ct_point_records WHERE related_type = 'evaluation' AND related_id = 2001;

-- 测试积分规则查询（应该使用rule_code唯一索引）
EXPLAIN SELECT * FROM ct_point_rules WHERE rule_code = 'test_evaluation_submit';

-- =============================================
-- 9. 清理测试数据
-- =============================================

SELECT '=== 测试完成，清理数据 ===' as test_section;

-- 注释掉清理语句，保留测试数据供查看
/*
SET FOREIGN_KEY_CHECKS = 0;
DELETE FROM ct_point_exchanges WHERE user_id IN (1001, 1002);
DELETE FROM ct_point_records WHERE user_id IN (1001, 1002);
DELETE FROM ct_user_points WHERE user_id IN (1001, 1002);
DELETE FROM ct_point_rules WHERE rule_code LIKE 'test_%';
DELETE FROM ct_reward_configs WHERE config_code LIKE 'test_%';
SET FOREIGN_KEY_CHECKS = 1;
*/

SELECT '积分和奖励系统表结构测试完成！' as test_result;