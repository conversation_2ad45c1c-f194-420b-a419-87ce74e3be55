# Product Overview

This is a Chinese mobile application called "次推" (CitUI) - a multi-platform app built with uni-app framework that appears to be focused on evaluation/assessment services with social features.

## Key Features
- **评测 (Evaluation)**: Core assessment/evaluation functionality
- **线索 (Clues)**: Information sharing and discovery
- **邀请 (Invite)**: User referral and invitation system
- **个人中心 (Profile)**: User account management

## Target Platforms
- WeChat Mini Program (primary)
- H5 Web App
- Android/iOS native apps
- Alipay Mini Program
- Baidu Mini Program
- ByteDance Mini Program

## Architecture
Full-stack application with:
- **Frontend**: uni-app (Vue 2) with uView UI components
- **Backend**: Laravel 9 PHP framework with WeChat integration
- **Database**: MySQL (based on SQL files present)

The app uses a tabbed navigation interface with custom navigation styling and focuses on mobile-first responsive design.