# Technology Stack

## Frontend (cituiapp/)
- **Framework**: uni-app (Vue 2 based)
- **UI Library**: uView UI 2.0.38
- **Build Tool**: HBuilderX/uni-app CLI
- **Styling**: SCSS with rpx units for responsive design
- **Additional Libraries**: 
  - uqrcodejs 4.0.7 (QR code generation)

## Backend (cituilaravel/)
- **Framework**: Laravel 9.x (PHP 8.0.2+)
- **Key Dependencies**:
  - WeChat SDK: w7corp/easywechat 6.7
  - Image Processing: intervention/image 2.7
  - Excel Import/Export: maatwebsite/excel 3.1
  - CAPTCHA: mews/captcha 3.3
  - Alibaba Cloud SDK: alibabacloud/client 1.5
  - TikTok MiniApp: 96qbhy/tt-microapp 2.4

## Development Tools
- **IDE Helper**: barryvdh/laravel-ide-helper (generates _ide_helper.php files)
- **Code Style**: Laravel Pint
- **Testing**: PHPUnit 9.5+

## Common Commands

### Frontend (uni-app)
```bash
# Development - use HBuilderX or uni-app CLI
# Build for different platforms through HBuilderX interface
# or use uni-app CLI commands for specific platforms
```

### Backend (Laravel)
```bash
# Install dependencies
composer install

# Generate application key
php artisan key:generate

# Run migrations
php artisan migrate

# Start development server
php artisan serve

# Generate IDE helper files
php artisan ide-helper:generate
php artisan ide-helper:models

# Clear caches
php artisan cache:clear
php artisan config:clear
php artisan route:clear
```

## Configuration Notes
- Frontend uses custom navigation styling (`navigationStyle: "custom"`)
- H5 deployment configured for `/m/` base path with history mode routing
- WeChat Mini Program integration requires proper appid configuration
- Multiple platform builds supported through uni-app's conditional compilation